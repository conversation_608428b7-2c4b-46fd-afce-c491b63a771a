import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { AdminLayout } from "@/components/layouts/AdminLayout";
import { withAdminAuth } from "@/lib/auth/admin";
import { GetServerSideProps } from "next";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { format } from "date-fns";
import { formatTimeAgo, FormatDate, getUserTimezone } from "@/lib/dayjs"; // Added getUserTimezone
import {
  User,
  Mail,
  Calendar,
  ChevronLeft,
  Shield,
  Lock,
  Activity,
  MessageCircle,
  AlertCircle,
  Ban,
  Download,
  MapPin,
  Users as UsersIcon,
  ExternalLink,
  Brain,
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { UserProfile } from "@/types";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

// Define the interface for event data
interface UserEvent {
  id: string;
  name: string;
  date: string | Date;
  location: string;
  createdOn: string | null;
  status: string;
  guests: {
    total: number;
    confirmed: number;
  };
}

export default function UserDetailPage() {
  const router = useRouter();
  const { userId } = router.query;
  const { toast } = useToast();
  const { data: session } = useSession();
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isToggling, setIsToggling] = useState(false);
  const [isTogglingAi, setIsTogglingAi] = useState(false);
  const [currentUserIsAdmin, setCurrentUserIsAdmin] = useState(false);

  // State for events management
  const [events, setEvents] = useState<UserEvent[]>([]);
  const [eventsLoading, setEventsLoading] = useState(false);
  const [eventsError, setEventsError] = useState<Error | null>(null);
  const [activeTab, setActiveTab] = useState("details");

  // Fetch user details
  useEffect(() => {
    const fetchUser = async () => {
      if (!userId) return;

      try {
        setLoading(true);
        const response = await fetch(`/api/admin/users/${userId}`);

        if (!response.ok) {
          throw new Error(`Error fetching user: ${response.statusText}`);
        }

        const data = await response.json();
        setUser(data.user);
        setCurrentUserIsAdmin(data.user.isAdmin || false);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch user details'));
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  // Fetch user's events when tab changes to "events"
  useEffect(() => {
    const fetchUserEvents = async () => {
      if (!userId || activeTab !== "events") return;

      try {
        setEventsLoading(true);
        setEventsError(null);

        const response = await fetch(`/api/admin/users/${userId}/events`);

        if (!response.ok) {
          throw new Error(`Error fetching events: ${response.statusText}`);
        }

        const data = await response.json();
        setEvents(data.events || []);
      } catch (err) {
        setEventsError(err instanceof Error ? err : new Error('Failed to fetch user events'));
        console.error("Error loading user events:", err);
      } finally {
        setEventsLoading(false);
      }
    };

    fetchUserEvents();
  }, [userId, activeTab]);

  // Handle send message (dummy function)
  const handleSendMessage = () => {
    toast({
      title: "Message Sent",
      description: `Your message would be sent to ${user?.email || user?.name}`,
    });
  };

  // Handle toggle admin status
  const handleToggleAdminStatus = async () => {
    if (!user || !userId || isToggling) return;

    if (user.id === session?.user?.id) {
      toast({
        title: "Action not allowed",
        description: "You cannot change your own admin status",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsToggling(true);
      const newAdminStatus = !user.isAdmin;

      const response = await fetch(`/api/admin/users/${userId}/toggle-admin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isAdmin: newAdminStatus })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update admin status');
      }

      const data = await response.json();
      setUser(prevUser => prevUser ? { ...prevUser, isAdmin: newAdminStatus } : null);

      toast({
        title: "Success",
        description: `${user.name || 'User'} is ${newAdminStatus ? 'now an admin' : 'no longer an admin'}`,
      });
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : 'Failed to update admin status',
        variant: "destructive"
      });
    } finally {
      setIsToggling(false);
    }
  };

  // Handle toggle AI access status
  const handleToggleAiAccessStatus = async () => {
    if (!user || !userId || isTogglingAi) return;

    try {
      setIsTogglingAi(true);
      const newAiAccessStatus = !user.hasAiAccess;

      const response = await fetch(`/api/admin/users/${userId}/toggle-ai-access`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ hasAiAccess: newAiAccessStatus })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update AI access status');
      }

      const data = await response.json();
      setUser(prevUser => prevUser ? { ...prevUser, hasAiAccess: newAiAccessStatus } : null);

      toast({
        title: "Success",
        description: `${user.name || 'User'} AI access ${newAiAccessStatus ? 'enabled' : 'disabled'}`,
      });
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : 'Failed to update AI access status',
        variant: "destructive"
      });
    } finally {
      setIsTogglingAi(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout pageTitle="User Details">
        <div className="space-y-6">
          <div className="flex items-center space-x-2">
            <Skeleton className="h-9 w-24" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-1">
              <Skeleton className="h-[400px] w-full" />
            </div>
            <div className="md:col-span-2">
              <Skeleton className="h-[400px] w-full" />
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout pageTitle="User Details">
        <div className="flex flex-col items-center justify-center h-64">
          <AlertCircle className="h-10 w-10 text-red-500 mb-2" />
          <h1 className="text-xl font-bold text-red-500">Error Loading User</h1>
          <p className="text-gray-500">{error.message}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => router.push('/admin/users')}
          >
            Back to Users List
          </Button>
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    return (
      <AdminLayout pageTitle="User Not Found">
        <div className="flex flex-col items-center justify-center h-64">
          <User className="h-10 w-10 text-gray-300 mb-2" />
          <h1 className="text-xl font-bold text-gray-500">User Not Found</h1>
          <p className="text-gray-400">The requested user could not be found.</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => router.push('/admin/users')}
          >
            Back to Users List
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout pageTitle={user.name || 'User Details'}>
      {/* Back button and title */}
      <div className="flex items-center space-x-4 mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back
        </Button>        
        <h1 className="text-2xl font-bold">User Details</h1>

        {user.isAdmin && (
          <Badge className="ml-2 bg-purple-100 text-purple-800 flex items-center gap-1">
            <Shield className="h-3 w-3" /> Admin
          </Badge>
        )}

        {user.hasAiAccess && (
          <Badge className="ml-2 bg-blue-100 text-blue-800 flex items-center gap-1">
            <Brain className="h-3 w-3" /> AI Access
          </Badge>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* User Info Card */}
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <div className="flex justify-center mb-2">
                <div className="relative">
                  <div className="w-24 h-24 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden">
                    {user.image ? (
                      <img
                        src={user.image}
                        alt={user.name || 'User'}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <User className="h-12 w-12 text-gray-400" />
                    )}
                  </div>
                  <Badge
                    className={`absolute bottom-1 right-1 ${user.isProfileComplete ? 'bg-green-100 text-green-800' : 'bg-gray-200 text-gray-700'}`}
                    variant="outline"
                  >
                    {user.isProfileComplete ? 'Complete' : 'Incomplete'}
                  </Badge>
                </div>
              </div>
              <CardTitle className="text-center">{user.name || 'Unnamed User'}</CardTitle>
              <CardDescription className="text-center">{user.email}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center">
                  <Mail className="h-4 w-4 mr-2 text-gray-500" />
                  <span>{user.email}</span>
                </div>
                {user.createdOn && (
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                    <span>Joined {formatTimeAgo(user.createdOn)}</span>
                  </div>
                )}
                {user.createdOn && (
                  <div className="ml-6 text-xs text-gray-500">
                    ({FormatDate(new Date(user.createdOn))})
                  </div>
                )}
                {user.hasGoogleLinked && (
                  <div className="flex items-center">
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">
                      Google Account Linked
                    </Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Admin Actions Card */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Admin Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                className="w-full flex items-center"
                onClick={handleSendMessage}
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                Send Message
              </Button>

              <Button
                variant="outline"
                className="w-full flex items-center"
                disabled
              >
                <Lock className="h-4 w-4 mr-2" />
                Reset Password
              </Button>

              <Button
                variant="outline"
                className="w-full flex items-center"
                disabled
              >
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>


              <Separator className="my-2" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="admin-toggle">Admin Status</Label>
                  <p className="text-xs text-muted-foreground">
                    {user.isAdmin
                      ? "This user has admin privileges"
                      : "Grant admin privileges to this user"}
                  </p>
                </div>
                <Switch
                  id="admin-toggle"
                  checked={!!user.isAdmin}
                  onCheckedChange={handleToggleAdminStatus}
                  disabled={isToggling || (user.id === session?.user?.id)}
                />
              </div>

              <Separator className="my-2" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="ai-access-toggle">AI Access</Label>
                  <p className="text-xs text-muted-foreground">
                    {user.hasAiAccess
                      ? "This user has access to AI features"
                      : "Grant access to AI features"}
                  </p>
                </div>
                <Switch
                  id="ai-access-toggle"
                  checked={!!user.hasAiAccess}
                  onCheckedChange={handleToggleAiAccessStatus}
                  disabled={isTogglingAi}
                />
              </div>

              <Separator className="my-2" />

              <Button
                variant="destructive"
                className="w-full flex items-center"
                disabled
              >
                <Ban className="h-4 w-4 mr-2" />
                Suspend Account
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* User Details Tabs */}
        <div className="md:col-span-2">
          <Tabs
            defaultValue="details"
            onValueChange={(value) => setActiveTab(value)}
          >
            <TabsList className="w-full">
              <TabsTrigger value="details" className="flex-1">User Details</TabsTrigger>
              <TabsTrigger value="activity" className="flex-1">Activity</TabsTrigger>
              <TabsTrigger value="events" className="flex-1">Events</TabsTrigger>
            </TabsList>

            {/* User Details Tab */}
            <TabsContent value="details">
              <Card>
                <CardHeader>
                  <CardTitle>User Information</CardTitle>
                  <CardDescription>
                    Detailed information about this user
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* User ID */}
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">User ID</h3>
                      <p className="mt-1 font-mono text-sm bg-gray-50 p-2 rounded">{user.id}</p>
                    </div>
                    
                    {/* Admin Status */}
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Admin Status</h3>
                      <div className="mt-1">
                        <Badge
                          variant={user.isAdmin ? "default" : "outline"}
                          className={user.isAdmin ? "bg-purple-100 text-purple-800 hover:bg-purple-100" : ""}
                        >
                          {user.isAdmin ? "Admin" : "Regular User"}
                        </Badge>
                      </div>
                    </div>

                    {/* AI Access Status */}
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">AI Access</h3>
                      <div className="mt-1">
                        <Badge
                          variant={user.hasAiAccess ? "default" : "outline"}
                          className={user.hasAiAccess ? "bg-blue-100 text-blue-800 hover:bg-blue-100" : ""}
                        >
                          {user.hasAiAccess ? "Enabled" : "Disabled"}
                        </Badge>
                      </div>
                    </div>

                    {/* Profile Status */}
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Profile Status</h3>
                      <div className="mt-1">
                        <Badge
                          variant={user.isProfileComplete ? "default" : "outline"}
                          className={user.isProfileComplete ? "bg-green-100 text-green-800 hover:bg-green-100" : ""}
                        >
                          {user.isProfileComplete ? "Complete" : "Incomplete"}
                        </Badge>
                      </div>
                    </div>

                    {/* Google Account */}
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Google Account</h3>
                      <div className="mt-1">
                        {user.hasGoogleLinked ? (
                          <div className="flex flex-col">
                            <Badge className="inline-flex w-fit bg-blue-50 text-blue-700 hover:bg-blue-50">
                              Linked
                            </Badge>
                            {user.googleEmail && (
                              <span className="text-sm mt-1">{user.googleEmail}</span>
                            )}
                          </div>
                        ) : (
                          <Badge variant="outline" className="bg-gray-50">
                            Not Linked
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Creation Date */}
                    {user.createdOn && (
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Created On</h3>
                        <div className="mt-1 flex flex-col">
                          <span>{new Date(user.createdOn).toLocaleString()}</span>
                          <span className="text-sm text-gray-500">({formatTimeAgo(user.createdOn)})</span>
                        </div>
                      </div>
                    )}

                    {/* Last Updated */}
                    {user.lastUpdatedOn && (
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Last Updated</h3>
                        <div className="mt-1 flex flex-col">
                          <span>{new Date(user.lastUpdatedOn).toLocaleString()}</span>
                          <span className="text-sm text-gray-500">({formatTimeAgo(user.lastUpdatedOn)})</span>
                        </div>
                      </div>
                    )}

                    {/* Additional profile fields would go here */}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Activity Tab */}
            <TabsContent value="activity">
              <Card>
                <CardHeader>
                  <CardTitle>User Activity</CardTitle>
                  <CardDescription>
                    Recent activity for this user
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center h-40">
                    <div className="text-center">
                      <Activity className="mx-auto h-8 w-8 text-gray-300" />
                      <p className="mt-2 text-gray-500">No recent activity recorded.</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Events Tab */}
            <TabsContent value="events">
              <Card>
                <CardHeader>
                  <CardTitle>User Events</CardTitle>
                  <CardDescription>
                    Events created by this user
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {eventsLoading ? (
                    // Loading state
                    <div className="space-y-4">
                      {Array.from({ length: 3 }).map((_, i) => (
                        <div key={`skeleton-${i}`} className="flex flex-col space-y-3">
                          <Skeleton className="h-5 w-full" />
                          <Skeleton className="h-4 w-2/3" />
                          <Skeleton className="h-4 w-1/2" />
                          <Separator className="my-2" />
                        </div>
                      ))}
                    </div>
                  ) : eventsError ? (
                    // Error state
                    <div className="flex flex-col items-center justify-center h-40 text-center">
                      <AlertCircle className="h-8 w-8 text-red-400 mb-2" />
                      <p className="text-red-500 font-medium">Error loading events</p>
                      <p className="text-sm text-gray-500 mt-1">{eventsError.message}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-4"
                        onClick={() => {
                          setEventsLoading(true);
                          setEventsError(null);
                          fetch(`/api/admin/users/${userId}/events`)
                            .then(res => {
                              if (!res.ok) throw new Error('Failed to load events');
                              return res.json();
                            })
                            .then(data => {
                              setEvents(data.events || []);
                            })
                            .catch(err => {
                              setEventsError(err);
                            })
                            .finally(() => {
                              setEventsLoading(false);
                            });
                        }}
                      >
                        Try Again
                      </Button>
                    </div>
                  ) : events.length === 0 ? (
                    // Empty state
                    <div className="flex items-center justify-center h-40">
                      <div className="text-center">
                        <Calendar className="mx-auto h-8 w-8 text-gray-300" />
                        <p className="mt-2 text-gray-500">No events found for this user.</p>
                      </div>
                    </div>
                  ) : (
                    // Events data
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Event Name</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Location</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {events.map((event) => (
                            <TableRow key={event.id}>
                              <TableCell className="font-medium">{event.name}</TableCell>
                              <TableCell>
                                {event.date ? FormatDate(new Date(event.date)) : "Not set"}
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center">
                                  <MapPin className="h-3.5 w-3.5 mr-1 text-gray-500" />
                                  <span className="truncate max-w-[150px]">{event.location || "No location"}</span>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge
                                  variant={event.status === "active" ? "default" : "outline"}
                                  className={
                                    event.status === "active"
                                      ? "bg-green-100 text-green-800 hover:bg-green-100"
                                      : event.status === "pending_payment"
                                        ? "bg-amber-100 text-amber-800 hover:bg-amber-100"
                                        : ""
                                  }
                                >
                                  {event.status === "active"
                                    ? "Active"
                                    : event.status === "pending_payment"
                                      ? "Pending Payment"
                                      : event.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <Link href={`/event/${event.id}`} passHref>
                                  <Button variant="outline" size="sm">
                                    <ExternalLink className="h-3.5 w-3.5 mr-1" />
                                    View
                                  </Button>
                                </Link>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </AdminLayout>
  );
}

// Server-side protection for admin routes
export const getServerSideProps: GetServerSideProps = withAdminAuth();