import { CSVInvite, UserProfile, Organization } from "@/types";
import { FireBaseAdmin, FireBaseAdminApp, FireBaseAdminFirestore, FireStore } from "./firebase";
import { GenerateID } from "./ID";
import { hashPassword } from "./auth/password";
import crypto from 'crypto';

interface Filter { field: string, operator: FireStore.WhereFilterOp, value: string }

/**
 * Represents a singleton database manager for Firebase Realtime Database.
 * Provides methods to read data from the database.
 * @example
 * const dbInstance = Database.getInstance();
 * const userPath = 'users/alovelace';
 * dbInstance.readDataOnce(userPath).then((user) => {
 *   console.log('User details:');
 *   console.log('Name:', user.name);
 *   console.log('Email:', user.email);
 * });
 */
export class Database {
  private static instance: Database;
  private static admin: FireBaseAdminApp | undefined;
  private db: FireBaseAdminFirestore;

  /**
   * Private constructor to enforce singleton pattern.
   */
  private constructor() {
    // Only initialize on the server side
    if (typeof window !== 'undefined') {
      throw new Error('Database can only be used on the server side');
    }
    const admin = FireBaseAdmin.getInstance();
    if (!admin) {
      throw new Error('Failed to initialize Firebase Admin');
    }
    this.db = admin.firestore();
  }

  /**
 * Normalize the ID to lowercase. First Alphabet is Capital. rest all alphabets are small.
 * @param {string} id The ID to normalize.
 * @returns {string} The normalized ID.
 */
  public static normalizeId(id: string): string {
    return id.charAt(0).toUpperCase() + id.slice(1).toLowerCase();
  }

  /**
   * Get the singleton instance of the Database class.
   * @returns {Database} The singleton instance.
   */
  public static getInstance(): Database {
    // Only get instance on the server side
    if (typeof window !== 'undefined') {
      throw new Error('Database can only be used on the server side');
    }

    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }

  public static async getFirestore(): Promise<FireBaseAdminFirestore> {
    if (!Database.admin) {
      Database.admin = FireBaseAdmin.getInstance();
    }
    if (!Database.admin) {
      throw new Error('Failed to initialize Firebase Admin');
    }
    return Database.admin.firestore();
  }

  /**
   * Adds data to a specified collection.
   *
   * @param {string} collection - The name of the collection.
   * @param {Record<string, any>} data - The data to add to the collection.
   * @returns {Promise<void>} A promise that resolves when the data is added.
   *
   * @example
   * const db = Database.getInstance();
   * db.addData('users', { name: 'Ada Lovelace', email: '
   */
  public addBulkData(collection: string, data: CSVInvite[]) {
    const batch = this.db.batch();
    data.forEach((item) => {
      if (item.ID) {
        const docRef = this.db.collection(collection).doc(item.ID);
        batch.set(docRef, item);
      } else {
        console.error('Item ID is undefined:', item);
      }
    });
    return batch.commit();
  }

  /**
 * Updates data in a specified collection and document path.
 *
 * @param {string} collection - The name of the collection.
 * @param {string} path - The document path within the collection.
 * @param {Record<string, any>} data - The data to update in the document.
 * @returns {Promise<void>} A promise that resolves when the update is complete.
 *
 * @example
 * const db = Database.getInstance();
 * db.updateData('users', 'user123', { age: 30 })
 *   .then(() => console.log('Document successfully updated!'))
 *   .catch((error) => console.error('Error updating document: ', error));
 */
  public updateData(collection: string, path: string, data: Record<string, any>) {
    const docRef = this.db.collection(collection).doc(path);
    return docRef.update(data);
  }

  /**
   * Read data from the database once.
   * @param {string} path The path to the data to read.
   * @returns {Promise<any>} A promise that resolves with the data at the specified path.
   */
  public readData(collection: string, path: string): Promise<any> {
    return this.db.collection(collection).doc(path).get().then((doc) => {
      if (doc.exists) {
        return doc.data()
      } else {
        return null;
      }
    });
  }

  public addData(collection: string, data: any): Promise<FireStore.WriteResult> {
    const documentId = data.ID || data.id; // Support both `ID` and `id` fields
    if (!documentId || typeof documentId !== 'string') {
      throw new Error(`Invalid document ID: ${documentId}. Document ID must be a non-empty string.`);
    }
    return this.db.collection(collection).doc(documentId).set(data);
  }




  public ListData<T>(collection: string, filter: Filter): Promise<T[]> {
    if (filter.value === undefined) {
      throw new Error(`Filter value for field "${filter.field}" is undefined. Please provide a valid value.`);
    }
    return this.db.collection(collection).where(filter.field, filter.operator, filter.value).get().then((querySnapshot) => {
      const data: T[] = [];
      querySnapshot.forEach((doc) => {
        data.push(doc.data() as T);
      });
      return data;
    });
  }

  /**
   * Returns a reference to a collection to allow direct query operations
   * like where, orderBy, limit, etc. to be chained.
   * 
   * @param {string} collection - The name of the collection to query.
   * @returns A query reference to the collection for further query operations.
   * 
   * @example
   * const db = Database.getInstance();
   * const query = db.query('feedback')
   *   .where('status', '==', 'new')
   *   .orderBy('createdAt', 'desc')
   *   .limit(10);
   * const snapshot = await query.get();
   * const items = snapshot.docs.map(doc => doc.data());
   */
  public query(collection: string): FireStore.Query<FireStore.DocumentData> {
    return this.db.collection(collection);
  }

  /**
   * Get user profile data from Firestore
   * @param userId The user's ID
   * @returns Promise<UserProfile | null>
   */
  public async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const doc = await this.db.collection('users').doc(userId).get();
      if (!doc.exists) {
        return null;
      }
      const data = doc.data() as UserProfile;
      return {
        ...data,
        id: doc.id,
        isProfileComplete: Boolean(data.name && data.email)
      };
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  }

  /**
   * Update user profile data in Firestore
   * @param userId The user's ID
   * @param profileData The profile data to update
   * @returns Promise<void>
   */
  public async updateUserProfile(userId: string, profileData: Partial<UserProfile>): Promise<void> {
    try {
      await this.db.collection('users').doc(userId).update({
        ...profileData,
        isProfileComplete: Boolean(profileData.name && profileData.email),
        lastUpdatedOn: (new Date()).toISOString(),
      });
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  /**
   * Deletes data from a specified collection and document path.
   *
   * @param {string} collection - The name of the collection.
   * @param {string} path - The document path within the collection.
   * @returns {Promise<FireStore.WriteResult>} A promise that resolves with the write result.
   */
  public deleteData(collection: string, path: string): Promise<FireStore.WriteResult> {
    const docRef = this.db.collection(collection).doc(path);
    return docRef.delete();
  }

  /**
   * Link a Google account to an existing user profile
   * @param userId The user's ID
   * @param googleProfile The Google profile information
   * @returns Promise<void>
   */
  public async linkGoogleAccount(userId: string, googleProfile: { id: string, email: string, image?: string }): Promise<void> {
    try {
      await this.db.collection('users').doc(userId).update({
        googleId: googleProfile.id,
        googleEmail: googleProfile.email,
        hasGoogleLinked: true,
        // Update image if user doesn't have one but Google does
        ...(googleProfile.image && !await this.getUserProfile(userId)?.then(profile => profile?.image)
          ? { image: googleProfile.image } : {})
      });
    } catch (error) {
      console.error('Error linking Google account:', error);
      throw error;
    }
  }

  /**
   * Unlink a Google account from an existing user profile
   * @param userId The user's ID
   * @returns Promise<void>
   */
  public async unlinkGoogleAccount(userId: string): Promise<void> {
    try {
      // First, get the user profile to check if the image came from Google
      const userProfile = await this.getUserProfile(userId);

      // Prepare update data
      const updateData: Record<string, any> = {
        googleId: null,
        googleEmail: null,
        hasGoogleLinked: false
      };

      // If the user has an image and no original image stored, remove the image as well
      // This assumes the image came from Google if there's no record of a user-uploaded image
      if (userProfile?.image && !userProfile.originalImage) {
        updateData.image = null;
      }

      // Update the user profile
      await this.db.collection('users').doc(userId).update(updateData);
    } catch (error) {
      console.error('Error unlinking Google account:', error);
      throw error;
    }
  }

  /**
   * Check if a Google account is already linked to another user
   * @param googleId The Google ID to check
   * @returns Promise<boolean>
   */
  public async isGoogleAccountLinked(googleId: string): Promise<boolean> {
    try {
      const snapshot = await this.db.collection('users')
        .where('googleId', '==', googleId)
        .limit(1)
        .get();

      return !snapshot.empty;
    } catch (error) {
      console.error('Error checking if Google account is linked:', error);
      return false;
    }
  }

  /**
   * Check if a user exists in the database
   * @param userId The user's ID to check
   * @returns Promise<boolean>
   */
  public async userExists(userId: string): Promise<boolean> {
    try {
      const doc = await this.db.collection('users').doc(userId).get();
      return doc.exists;
    } catch (error) {
      console.error('Error checking if user exists:', error);
      return false;
    }
  }

  /**
   * Get a user profile by email
   * @param email The user's email address
   * @returns Promise<UserProfile | null>
   */
  public async getUserByEmail(email: string): Promise<UserProfile | null> {
    try {
      const snapshot = await this.db.collection('users')
        .where('email', '==', email)
        .limit(1)
        .get();

      if (snapshot.empty) {
        return null;
      }

      const doc = snapshot.docs[0];
      const data = doc.data() as UserProfile;

      return {
        ...data,
        id: doc.id,
        isProfileComplete: Boolean(data.name && data.email)
      };
    } catch (error) {
      console.error('Error getting user by email:', error);
      return null;
    }
  }

  /**
   * Create a new user profile in Firestore
   * @param profileData The complete profile data to create
   * @returns Promise<void>
   */
  public async createUserProfile(profileData: UserProfile): Promise<void> {
    try {
      // Ensure we have an ID
      if (!profileData.id) {
        throw new Error('User ID is required to create a profile');
      }

      const now = new Date();

      // Use set instead of update when creating a new document
      await this.db.collection('users').doc(profileData.id).set({
        ...profileData,
        isProfileComplete: Boolean(profileData.name && profileData.email),
        createdOn: now.toISOString(),
        lastUpdatedOn: now.toISOString(),
      });

      // Create an individual organization for the new user
      const organization: Organization = {
        id: profileData.id, // Use user ID as organization ID for simplicity
        name: profileData.name || "Unnamed Organization",
        type: "individual",
        members: [
          {
            userId: profileData.id,
            role: "owner",
          },
        ],
        createdOn: now.toISOString(),
        lastUpdatedOn: now.toISOString(),
      };

      // Add the organization to the database
      await this.db.collection('organizations').doc(organization.id).set(organization);
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  }

  /**
   * Get all users from the database with pagination and search
   * @param options Pagination, search, and sorting options
   * @returns Promise<{users: UserProfile[], total: number}>
   */
  public async getAllUsers(options?: {
    page?: number,
    limit?: number,
    searchQuery?: string,
    sortBy?: string,
    sortOrder?: 'asc' | 'desc'
  }): Promise<{ users: UserProfile[], total: number }> {
    try {
      const {
        page = 1,
        limit = 10,
        searchQuery = '',
        sortBy = 'createdOn',
        sortOrder = 'desc'
      } = options || {};

      // Calculate pagination values
      const offset = (page - 1) * limit;

      // Get reference to users collection
      let query = this.db.collection('users');

      // We need to get all users for search and sorting since Firestore has limitations
      const allSnapshots = await query.get();
      const allUsers: UserProfile[] = [];

      allSnapshots.forEach((doc) => {
        const data = doc.data() as UserProfile;
        const user = {
          ...data,
          id: doc.id,
          isProfileComplete: Boolean(data.name && data.email)
        };
        allUsers.push(user);
      });

      // Apply search filter if provided
      let filteredUsers = allUsers;
      if (searchQuery) {
        const lowerSearchQuery = searchQuery.toLowerCase();
        filteredUsers = allUsers.filter(user =>
          (user.name?.toLowerCase().includes(lowerSearchQuery) || false) ||
          (user.email?.toLowerCase().includes(lowerSearchQuery) || false)
        );
      }

      // Apply sorting
      filteredUsers.sort((a, b) => {
        let aValue: any;
        let bValue: any;

        switch (sortBy) {
          case 'name':
            aValue = a.name?.toLowerCase() || '';
            bValue = b.name?.toLowerCase() || '';
            break;
          case 'email':
            aValue = a.email?.toLowerCase() || '';
            bValue = b.email?.toLowerCase() || '';
            break;
          case 'createdOn':
          default:
            aValue = a.createdOn ? new Date(a.createdOn).getTime() : 0;
            bValue = b.createdOn ? new Date(b.createdOn).getTime() : 0;
            break;
        }

        if (sortOrder === 'asc') {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
      });

      // Get total count after filtering
      const total = filteredUsers.length;

      // Apply pagination
      const paginatedUsers = filteredUsers.slice(offset, offset + limit);

      return { users: paginatedUsers, total };
    } catch (error) {
      console.error('Error fetching users with pagination:', error);
      return { users: [], total: 0 };
    }
  }

  /**
   * Get a user by ID
   * @param userId The user's ID (could be document ID or Google ID)
   * @returns Promise<UserProfile | null>
   */
  public async getUserById(userId: string): Promise<UserProfile | null> {
    try {
      // First try to get the user by document ID
      const userByDocId = await this.getUserProfile(userId);
      if (userByDocId) {
        return userByDocId;
      }

      // If not found, check if this is a Google ID
      if (userId.length > 15) { // Google IDs are typically long numeric strings
        const snapshot = await this.db.collection('users')
          .where('googleId', '==', userId)
          .limit(1)
          .get();

        if (!snapshot.empty) {
          const doc = snapshot.docs[0];
          const data = doc.data() as UserProfile;
          return {
            ...data,
            id: doc.id,
            isProfileComplete: Boolean(data.name && data.email)
          };
        }
      }

      // User not found by either document ID or Google ID
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Set or unset a user's admin status
   * @param userId The user's ID
   * @param isAdmin Boolean to set or unset admin status
   * @returns Promise<void>
   */
  public async setUserAdminStatus(userId: string, isAdmin: boolean): Promise<void> {
    try {
      await this.db.collection('users').doc(userId).update({
        isAdmin: isAdmin
      });
    } catch (error) {
      console.error('Error updating user admin status:', error);
      throw error;
    }
  }

  /**
   * Set or unset a user's AI access status
   * @param userId The user's ID
   * @param hasAiAccess Boolean to set or unset AI access
   * @returns Promise<void>
   */
  public async setUserAiAccessStatus(userId: string, hasAiAccess: boolean): Promise<void> {
    try {
      await this.db.collection('users').doc(userId).update({
        hasAiAccess: hasAiAccess
      });
    } catch (error) {
      console.error('Error updating user AI access status:', error);
      throw error;
    }
  }

  /**
   * Create a new user with email and password
   * @param email User's email
   * @param password User's password (will be hashed)
   * @param name User's name (optional)
   * @returns Promise<{id: string}> The ID of the created user
   */
  public async createUserWithPassword(email: string, password: string, name?: string): Promise<{ id: string }> {
    try {
      // Check if user with this email already exists
      const existingUser = await this.getUserByEmail(email);
      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Hash the password
      const passwordHash = await hashPassword(password);

      // Generate a unique ID for the user
      const userId = GenerateID('U');

      // Create the user profile
      await this.createUserProfile({
        id: userId,
        email,
        name: name || null,
        isProfileComplete: Boolean(name),
        isWelcomeEmailSent: false,
        passwordHash,
        hasGoogleLinked: false
      });

      return { id: userId };
    } catch (error) {
      console.error('Error creating user with password:', error);
      throw error;
    }
  }

  /**
   * Update a user's password
   * @param userId User's ID
   * @param newPassword New password (will be hashed)
   * @returns Promise<void>
   */
  public async updateUserPassword(userId: string, newPassword: string): Promise<void> {
    try {
      // Hash the new password
      const passwordHash = await hashPassword(newPassword);

      // Update the user's password hash
      await this.db.collection('users').doc(userId).update({
        passwordHash,
        resetPasswordToken: null,
        resetPasswordExpires: null,
        lastUpdatedOn: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error updating user password:', error);
      throw error;
    }
  }

  /**
   * Generate a password reset token for a user
   * @param email User's email
   * @returns Promise<{token: string, userId: string} | null> The reset token and user ID, or null if user not found
   */
  public async generatePasswordResetToken(email: string): Promise<{ token: string, userId: string } | null> {
    try {
      // Find the user by email
      const user = await this.getUserByEmail(email);
      if (!user) {
        return null;
      }

      // Generate a random token
      const token = crypto.randomBytes(32).toString('hex');

      // Set token expiration to 1 hour from now
      const expires = Date.now() + 3600000; // 1 hour in milliseconds

      // Update the user with the reset token and expiration
      await this.db.collection('users').doc(user.id).update({
        resetPasswordToken: token,
        resetPasswordExpires: expires,
        lastUpdatedOn: new Date().toISOString()
      });

      return { token, userId: user.id };
    } catch (error) {
      console.error('Error generating password reset token:', error);
      throw error;
    }
  }

  /**
   * Verify a password reset token
   * @param token The reset token to verify
   * @returns Promise<UserProfile | null> The user profile if token is valid, null otherwise
   */
  public async verifyPasswordResetToken(token: string): Promise<UserProfile | null> {
    try {
      // Find the user with this reset token
      const snapshot = await this.db.collection('users')
        .where('resetPasswordToken', '==', token)
        .limit(1)
        .get();

      if (snapshot.empty) {
        return null;
      }

      const doc = snapshot.docs[0];
      const user = doc.data() as UserProfile;

      // Check if token has expired
      if (!user.resetPasswordExpires || user.resetPasswordExpires < Date.now()) {
        return null;
      }

      return {
        ...user,
        id: doc.id,
        isProfileComplete: Boolean(user.name && user.email)
      };
    } catch (error) {
      console.error('Error verifying password reset token:', error);
      return null;
    }
  }

  /**
   * Reset a user's password using a token
   * @param token The reset token
   * @param newPassword The new password
   * @returns Promise<boolean> True if password was reset successfully
   */
  public async resetPasswordWithToken(token: string, newPassword: string): Promise<boolean> {
    try {
      // Verify the token and get the user
      const user = await this.verifyPasswordResetToken(token);
      if (!user) {
        return false;
      }

      // Hash the new password
      const passwordHash = await hashPassword(newPassword);

      // Update the user's password and clear the reset token
      await this.db.collection('users').doc(user.id).update({
        passwordHash,
        resetPasswordToken: null,
        resetPasswordExpires: null,
        lastUpdatedOn: new Date().toISOString()
      });

      return true;
    } catch (error) {
      console.error('Error resetting password with token:', error);
      return false;
    }
  }

  /**
   * Lists all documents in a collection
   * @param collection The name of the collection to list all documents from
   * @returns Promise with array of documents
   */
  public async ListAllData<T>(collection: string): Promise<T[]> {
    try {
      const snapshot = await this.db.collection(collection).get();
      const data: T[] = [];
      snapshot.forEach((doc) => {
        data.push({ ...doc.data(), ID: doc.id } as T);
      });
      return data;
    } catch (error) {
      console.error(`Error listing all data from ${collection}:`, error);
      throw error;
    }
  }

  /**
   * Get organization details by user ID
   * @param userId The user's ID
   * @returns Promise<Organization | null>
   */
  public async getOrganizationByUserId(userId: string): Promise<Organization | null> {
    try {
      // First, check if the organization exists with the same ID as the user
      // (This is the default case for individual users)
      const orgDoc = await this.db.collection('organizations').doc(userId).get();

      if (orgDoc.exists) {
        const data = orgDoc.data() as Organization;
        return {
          ...data,
          id: orgDoc.id
        };
      }

      // If not found by ID, check if the user is a member of any organization
      const snapshot = await this.db.collection('organizations')
        .where('members', 'array-contains', { userId, role: 'owner' })
        .limit(1)
        .get();

      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        const data = doc.data() as Organization;
        return {
          ...data,
          id: doc.id
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting organization by user ID:', error);
      return null;
    }
  }

  /**
   * Get organization details by ID
   * @param organizationId The organization's ID
   * @returns Promise<Organization | null>
   */
  public async getOrganizationById(organizationId: string): Promise<Organization | null> {
    try {
      const orgDoc = await this.db.collection('organizations').doc(organizationId).get();

      if (orgDoc.exists) {
        const data = orgDoc.data() as Organization;
        return {
          ...data,
          id: orgDoc.id
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting organization by ID:', error);
      return null;
    }
  }

  /**
   * Update organization details
   * @param organizationId The organization's ID
   * @param updateData The data to update
   * @returns Promise<void>
   */
  public async updateOrganization(organizationId: string, updateData: Partial<Organization>): Promise<void> {
    try {
      await this.db.collection('organizations').doc(organizationId).update(updateData);
    } catch (error) {
      console.error('Error updating organization:', error);
      throw error;
    }
  }

  /**
   * Fix organization members by updating Google IDs to document IDs
   * @param organizationId The organization's ID
   * @returns Promise<{fixed: number, total: number}>
   */
  public async fixOrganizationMembers(organizationId: string): Promise<{ fixed: number, total: number }> {
    try {
      // Get the organization
      const organization = await this.getOrganizationById(organizationId);

      if (!organization || !organization.members || organization.members.length === 0) {
        return { fixed: 0, total: 0 };
      }

      const members = [...organization.members];
      let fixedCount = 0;

      // Process each member
      for (let i = 0; i < members.length; i++) {
        const member = members[i];

        // Skip if the user already exists with this ID
        const userExists = await this.getUserProfile(member.userId);
        if (userExists) {
          continue;
        }

        // Try to find the user by Google ID
        if (member.userId.length > 15) { // Likely a Google ID
          const snapshot = await this.db.collection('users')
            .where('googleId', '==', member.userId)
            .limit(1)
            .get();

          if (!snapshot.empty) {
            const doc = snapshot.docs[0];
            // Update the member's userId to the document ID
            members[i] = {
              ...member,
              userId: doc.id
            };
            fixedCount++;
          }
        }
      }

      // Update the organization if any members were fixed
      if (fixedCount > 0) {
        await this.updateOrganization(organizationId, {
          members,
          lastUpdatedOn: new Date().toISOString()
        });
      }

      return { fixed: fixedCount, total: members.length };
    } catch (error) {
      console.error('Error fixing organization members:', error);
      throw error;
    }
  }

  /**
   * Get all organizations from the database with pagination and search
   * @param options Pagination and search options
   * @returns Promise<{organizations: Organization[], total: number}>
   */
  public async getAllOrganizations(options?: {
    page?: number,
    limit?: number,
    searchQuery?: string
  }): Promise<{ organizations: Organization[], total: number }> {
    try {
      const {
        page = 1,
        limit = 10,
        searchQuery = ''
      } = options || {};

      // Calculate pagination values
      const offset = (page - 1) * limit;

      // Get reference to organizations collection
      let query = this.db.collection('organizations');

      // Get total count (for pagination)
      const totalSnapshot = await query.get();
      let total = totalSnapshot.size;

      // Apply search filter if provided
      let filteredOrganizations: Organization[] = [];

      // Get all organizations
      const snapshot = await query.get();
      const allOrganizations = snapshot.docs.map(doc => ({
        ...doc.data(),
        id: doc.id
      })) as Organization[];

      // Apply search filter if provided
      if (searchQuery) {
        const lowerQuery = searchQuery.toLowerCase();
        filteredOrganizations = allOrganizations.filter(org =>
          org.name.toLowerCase().includes(lowerQuery) ||
          org.type.toLowerCase().includes(lowerQuery)
        );
        total = filteredOrganizations.length;
      } else {
        filteredOrganizations = allOrganizations;
      }

      // Apply pagination
      const paginatedOrganizations = filteredOrganizations
        .sort((a, b) => {
          // Sort by created date (newest first)
          const dateA = new Date(a.createdOn).getTime();
          const dateB = new Date(b.createdOn).getTime();
          return dateB - dateA;
        })
        .slice(offset, offset + limit);

      return {
        organizations: paginatedOrganizations,
        total
      };
    } catch (error) {
      console.error('Error getting all organizations:', error);
      return { organizations: [], total: 0 };
    }
  }

  /**
   * Mark an event as completed and trigger post-event actions
   * @param eventId The event's ID
   * @param organizationId The organization's ID
   * @returns Promise<void>
   */
  public async markEventAsCompleted(eventId: string, organizationId: string): Promise<void> {
    try {
      const now = new Date().toISOString();

      // Update the event status
      await this.updateData('events', eventId, {
        status: 'completed',
        completedAt: now,
        lastUpdatedOn: now
      });

      // Trigger post-event actions (this will be called by a separate process)
      await this.checkAndTriggerPostEventActions(eventId);
    } catch (error) {
      console.error('Error marking event as completed:', error);
      throw error;
    }
  }

  /**
   * Check if an event should trigger post-event actions and execute them
   * @param eventId The event's ID
   * @returns Promise<void>
   */
  public async checkAndTriggerPostEventActions(eventId: string): Promise<void> {
    try {
      const event = await this.readData('events', eventId);
      if (!event) {
        console.log(`Event ${eventId} not found`);
        return;
      }

      // Get the organization
      const organization = await this.getOrganizationById(event.ownerAccountId);
      if (!organization) {
        console.log(`Organization not found for event ${eventId}`);
        return;
      }

      // Check if we should send the contact group save email
      const shouldSendEmail = await this.shouldSendContactGroupSaveEmail(organization, eventId);

      if (shouldSendEmail) {
        // Import the email function dynamically to avoid circular dependencies
        const { sendPostEventContactGroupEmail } = await import('./mailer');
        await sendPostEventContactGroupEmail(organization.id, eventId);

        // Update organization settings to track that we've asked
        await this.updateOrganizationContactGroupSettings(organization.id, {
          hasBeenAsked: true
        });
      }
    } catch (error) {
      console.error('Error checking and triggering post-event actions:', error);
      throw error;
    }
  }

  /**
   * Check if we should send contact group save email to an organization
   * @param organization The organization
   * @param eventId The event ID
   * @returns Promise<boolean>
   */
  private async shouldSendContactGroupSaveEmail(organization: Organization, eventId: string): Promise<boolean> {
    // Check if we've already sent the one-time introduction email
    if (organization.contactGroupSettings?.hasBeenAsked) {
      return false;
    }

    // Check if the event has contact groups to save
    const { hasContactGroupsToSave } = await import('./saved-contact-groups');
    return await hasContactGroupsToSave(eventId);
  }

  /**
   * Update organization contact group settings
   * @param organizationId The organization's ID
   * @param settings The settings to update
   * @returns Promise<void>
   */
  public async updateOrganizationContactGroupSettings(
    organizationId: string,
    settings: Partial<Organization['contactGroupSettings']>
  ): Promise<void> {
    try {
      const organization = await this.getOrganizationById(organizationId);
      if (!organization) {
        throw new Error('Organization not found');
      }

      const updatedSettings = {
        hasBeenAsked: false,
        ...organization.contactGroupSettings,
        ...settings
      };

      await this.updateOrganization(organizationId, {
        contactGroupSettings: updatedSettings
      });
    } catch (error) {
      console.error('Error updating organization contact group settings:', error);
      throw error;
    }
  }

  /**
   * Get events that should be checked for completion (ended more than 24 hours ago)
   * @returns Promise<any[]>
   */
  public async getEventsToCheckForCompletion(): Promise<any[]> {
    try {
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

      const snapshot = await this.query('events')
        .where('eventDate', '<', twentyFourHoursAgo.toISOString())
        .where('status', '!=', 'completed')
        .get();

      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting events to check for completion:', error);
      return [];
    }
  }
}