import React, { useEffect } from "react";
import { useRouter } from "next/router";
import Link from "next/link";
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout";
import { Button } from "@/components/ui/button";
import { Users, Settings, LayoutDashboard, Shield, Building2, MessageSquare, Link2, TestTube } from "lucide-react";
import { Header } from "@/components/Header";
import { useSession } from "next-auth/react";

interface AdminLayoutProps {
  children: React.ReactNode;
  pageTitle?: string;
}

export function AdminLayout({ children, pageTitle }: AdminLayoutProps) {
  const router = useRouter();
  const { data: session } = useSession();

  // Check if user is admin and redirect if not
  useEffect(() => {
    if (session && !session.user?.isAdmin) {
      router.push('/access-denied');
    }
  }, [session, router]);

  // Generate breadcrumbs based on the current path
  const generateBreadcrumbs = () => {
    const pathSegments = router.pathname.split('/').filter(Boolean);
    const breadcrumbs = [];
    let path = '';

    // Add Admin as the first breadcrumb
    breadcrumbs.push({
      label: 'Admin',
      href: '/admin/dashboard'
    });
    // Add intermediate breadcrumbs (skip the 'admin' part as it's already added)
    for (let i = 1; i < pathSegments.length - 1; i++) {
      const segment = pathSegments[i];
      path += `/${segment}`;

      // Skip dynamic route parameters like [userId]
      if (segment.startsWith('[') && segment.endsWith(']')) continue;

      // Format the label to capitalize it
      const label = segment.charAt(0).toUpperCase() + segment.slice(1);

      breadcrumbs.push({
        label,
        href: `/admin${path}`
      });
    }

    return breadcrumbs;
  };

  const isActive = (path: string) => {
    return router.pathname.startsWith(path);
  };

  const breadcrumbs = generateBreadcrumbs();

  return (
    <ProtectedLayout isAdmin={true}>
      {/* Header with Breadcrumbs */}
      <Header
        title={pageTitle}
        breadcrumbs={breadcrumbs}
        showUserProfile={true}
      />

      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto p-4">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Sidebar */}
            <aside className="w-full md:w-64 bg-white p-4 rounded-lg shadow-sm">
              <h2 className="text-xl font-bold mb-6">Admin Panel</h2>
              <nav className="space-y-2">
                <Link href="/admin/dashboard" passHref>
                  <Button
                    variant={isActive("/admin/dashboard") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <LayoutDashboard className="mr-2 h-4 w-4" />
                    Dashboard
                  </Button>
                </Link>
                <Link href="/admin/users" passHref>
                  <Button
                    variant={isActive("/admin/users") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <Users className="mr-2 h-4 w-4" />
                    Users
                  </Button>
                </Link>
                <Link href="/admin/organizations" passHref>
                  <Button
                    variant={isActive("/admin/organizations") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <Building2 className="mr-2 h-4 w-4" />
                    Organizations
                  </Button>
                </Link>
                <Link href="/admin/feedback" passHref>
                  <Button
                    variant={isActive("/admin/feedback") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Feedback
                  </Button>
                </Link>
                <Link href="/admin/links" passHref>
                  <Button
                    variant={isActive("/admin/links") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <Link2 className="mr-2 h-4 w-4" />
                    Links
                  </Button>
                </Link>
                <Link href="/test/contact-groups" passHref>
                  <Button
                    variant={isActive("/test/contact-groups") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <TestTube className="mr-2 h-4 w-4" />
                    Contact Groups Test
                  </Button>
                </Link>
                <Link href="/admin/settings" passHref>
                  <Button
                    variant={isActive("/admin/settings") ? "default" : "ghost"}
                    className="w-full justify-start"
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </Button>
                </Link>
              </nav>
            </aside>

            {/* Main Content */}
            <main className="flex-1 bg-white p-6 rounded-lg shadow-sm">
              {children}
            </main>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  );
}