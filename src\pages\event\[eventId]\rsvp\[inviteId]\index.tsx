'use client';

import ChatView from "@/components/ChatView"
import ConfirmationView from "@/components/ConfirmationView"
import DigitalInvitePreview from "@/components/DigitalInvitePreview"
import RSVPDialog from "@/components/RSVPDialog"
import { useRSVP } from "@/hooks/useRSVP"
import { GetEventDateTimes, ToGoogleFormat } from "@/lib/dayjs"
import { useEffect, useState, useRef, useCallback } from "react"
import { useRouter } from "next/router"
import { useEvent } from "@/hooks/useEvent"
import { useInvites } from "@/hooks/useInvites"
import { EventInvite } from "@/types"
import { useSession } from "next-auth/react"
import { useSearchParams } from "next/navigation";

export default function RSVPForm() {
  const router = useRouter()
  const { eventId, inviteId } = router.query
  const { event, loading: eventLoading } = useEvent(eventId as string)
  const { invites, loading: invitesLoading } = useInvites(eventId as string)
  const [invite, setInvite] = useState<EventInvite | null>(null)
  const [view, setView] = useState<"invite" | "confirmation" | "chat">("invite")
  const [rsvpDialogOpen, setRsvpDialogOpen] = useState(false)
  const [messages, setMessages] = useState<Array<{ id: string; text: string; sender: "guest" | "host"; timestamp: Date }>>([])
  const [newMessage, setNewMessage] = useState("")
  const { data: session } = useSession()
  const searchParams = useSearchParams();
  const theme = searchParams?.get("theme");

  useEffect(() => {
    if (theme === "dark") {
      document.body.classList.add("dark");
    } else {
      document.body.classList.remove("dark");
    }
  }, [theme]);

  useEffect(() => {
    if (inviteId === "preview" || inviteId === "preview-accepted" || inviteId === "preview-declined") {
      const dummyInvite: EventInvite = {
        ID: "dummy",
        eventId: eventId as string,
        name: "Guest",
        status: inviteId === "preview" ? "invited" : inviteId === "preview-accepted" ? "accepted" : "declined",
        message: inviteId === "preview-accepted" ? [{
          id: "1",
          content: "Thank you for accepting the invite!",
          sender: "host",
          timestamp: new Date()
        }] : inviteId === "preview-declined" ? [{
          id: "2",
          content: "Sorry to see you decline the invite.",
          sender: "host",
          timestamp: new Date()
        }] : [],
        adults: 1,
        children: 0
      };

      // Preview mode - use event data as-is without adding static fallbacks

      setInvite(dummyInvite);
      setView("invite");
    } else if (invites && inviteId) {
      const foundInvite = invites.find((i) => i.ID === inviteId);
      if (foundInvite) {
        setInvite({
          ...foundInvite,
          message: [],
        } as EventInvite);
      }
    }
  }, [event, invites, inviteId, eventId])

  // Use a ref to track the last time we recorded an open
  const lastTrackedTime = useRef<number>(0);

  const trackInviteOpen = useCallback(async () => {
    try {
      // Determine if current user is event owner or manager
      const isOwner = session?.user?.email === event?.ownerEmail;
      const isManager = event?.managers?.includes(session?.user?.id as string) || false;

      // Only track if eventId and inviteId are strings
      if (typeof eventId === 'string' && typeof inviteId === 'string') {
        console.log('Tracking invite open at', new Date().toISOString());
        // Update the last tracked time before the API call
        lastTrackedTime.current = Date.now();

        // Call API to track the invite open
        await fetch(`/api/event/${eventId}/invites/${inviteId}/track-open`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            isOwner,
            isManager
          }),
        });
      }
    } catch (error) {
      console.error('Error tracking invite open:', error);
    }
  }, [eventId, inviteId, event?.ownerEmail, event?.managers, session?.user?.email, session?.user?.id]);

  // Track invite link open - once per page load and then every time the user navigates back to the page
  useEffect(() => {
    // Only track if we have both eventId and inviteId
    if (eventId && inviteId && event && !eventLoading && !invitesLoading) {
      const now = Date.now();
      // Only track if it's been at least 2 seconds since the last tracking
      // This prevents duplicate entries from component re-renders
      if (now - lastTrackedTime.current > 2000) {
        trackInviteOpen();
      }
    }
  }, [eventId, inviteId, event, eventLoading, invitesLoading, trackInviteOpen]);

  useEffect(() => {
    if (view === "confirmation") {
      console.log("View changed to confirmation")
    }
  }, [view])

  function handleSendMessage(): void {
    // Implement message sending logic here
  }

  function addToCalendar(): void {
    if (!event) return

    // Track add to calendar action
    fetch(`/api/event/${eventId}/invites/${inviteId}/track-calendar`, {
      method: 'POST',
    }).catch(err => console.error('Error tracking calendar add:', err));

    const dates = GetEventDateTimes(event)
    const title = encodeURIComponent(event.eventName)
    const start = encodeURIComponent(ToGoogleFormat(dates.start))
    const end = encodeURIComponent(ToGoogleFormat(dates.end))
    const location = encodeURIComponent(event.location)
    const details = encodeURIComponent(event.message)
    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${start.replace(/-|:|\.\d+/g, '')}/${end.replace(/-|:|\.\d+/g, '')}&details=${details}&location=${location}`
    window.open(googleCalendarUrl, "_blank")
  }

  const onResponded = () => {
    // Refresh the invite data
    if (eventId && inviteId) {
      fetch(`/api/event/${eventId}/invites/${inviteId}`)
        .then(res => res.json())
        .then(data => {
          setInvite(data)
          setView("confirmation")
        })
    }
  }

  if (eventLoading || invitesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!event || !invite) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-gray-500">Invite not found</p>
      </div>
    )
  }

  if (view === "chat") {
    return (
      <ChatView
        messages={messages}
        newMessage={newMessage}
        setNewMessage={setNewMessage}
        handleSendMessage={handleSendMessage}
        setView={setView}
      />
    )
  }

  if (view === "confirmation") {
    return (
      <ConfirmationView
        event={event}
        invite={invite}
        setRsvpDialogOpen={setRsvpDialogOpen}
        addToCalendar={addToCalendar}
        setView={setView}
      />
    )
  }

  // Use the DigitalInvitePreview for all invites
  return (
    <div className="flex flex-col min-h-screen">
      <DigitalInvitePreview
        event={event}
        invite={invite}
        setRsvpDialogOpen={setRsvpDialogOpen}
        addToCalendar={addToCalendar}
      />
      <RSVPDialog
        rsvpDialogOpen={rsvpDialogOpen}
        setRsvpDialogOpen={setRsvpDialogOpen}
        event={event}
        invite={invite}
        onRSVPResponded={onResponded}
      />
    </div>
  )
}