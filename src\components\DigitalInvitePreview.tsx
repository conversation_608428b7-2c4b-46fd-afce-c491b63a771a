/* eslint-disable @next/next/no-img-element */
import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Clock, MapPin, Copy, Check, Hourglass } from "lucide-react"
import { Event, EventInvite } from "@/types";
import { FormatDate } from "@/lib/dayjs";
import { Format24to12 } from "@/lib/time";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";

interface DigitalInvitePreviewProps {
  event: Event;
  invite: EventInvite;
  setRsvpDialogOpen: (open: boolean) => void;
  addToCalendar: () => void;
}

export default function DigitalInvitePreview({
  event,
  invite,
  setRsvpDialogOpen,
  addToCalendar
}: DigitalInvitePreviewProps) {

  // State for background image
  const [backgroundImage, setBackgroundImage] = React.useState<string | null>(null);

  // State for expanded message
  const [isExpanded, setIsExpanded] = React.useState(false);

  // State for copied email
  const [emailCopied, setEmailCopied] = React.useState(false);

  // Toast hook for notifications
  const { toast } = useToast();

  // Check if event date has passed
  const isEventOver = React.useMemo(() => {
    if (!event.eventDate) return false;

    const now = new Date();
    const eventDate = new Date(event.eventDate);

    // If event has end time, use it to determine if event is over
    if (event.end) {
      const [hours, minutes] = event.end.split(':').map(Number);
      eventDate.setHours(hours, minutes);
    } else {
      // Default to end of day if no end time specified
      eventDate.setHours(23, 59, 59);
    }

    return now > eventDate;
  }, [event.eventDate, event.end]);

  // Check if RSVP deadline has passed
  const isRsvpClosed = React.useMemo(() => {
    if (!event.rsvpDueDate) return false;

    const now = new Date();
    const dueDate = new Date(event.rsvpDueDate);

    return now > dueDate;
  }, [event.rsvpDueDate]);

  // Get the first letter of the host name for the avatar
  const hostFirstLetter = event.host ? event.host.charAt(0).toUpperCase() : "A";

  // Function to copy email to clipboard
  const copyEmailToClipboard = () => {
    if (hostEmail) {
      navigator.clipboard.writeText(hostEmail)
        .then(() => {
          setEmailCopied(true);
          toast({
            title: "Email copied!",
            description: "The host's email has been copied to your clipboard.",
          });
          setTimeout(() => setEmailCopied(false), 2000);
        })
        .catch(err => {
          console.error("Failed to copy email:", err);
          toast({
            title: "Copy failed",
            description: "Could not copy the email. Please try again.",
            variant: "destructive",
          });
        });
    }
  };

  // Get host email - in a real app, this would come from the event data
  const hostEmail = event.ownerEmail;  // Try to fetch the image on component mount
  React.useEffect(() => {
    // Try to fetch the image URL from storage API with fallback logic
    const timestamp = Date.now();
    fetch(`/api/storage/getUrl?eventId=${event.ID}&imageType=digital-invite&_t=${timestamp}`)
      .then(response => response.json())
      .then(data => {
        if (data.exists && data.url) {
          // Add a cache-busting parameter to ensure fresh image
          const separator = data.url.includes('?') ? '&' : '?';
          const imageUrlWithCache = data.url + separator + 'cache=' + timestamp + '&_refresh=' + timestamp;
          setBackgroundImage(imageUrlWithCache);
        }
      })
      .catch(() => {
        toast({
          title: "Image loading error",
          description: "Could not load the event image. Using placeholder instead.",
          variant: "destructive",
        });
      });
  }, [event.ID, toast]);

  return (
    <div className="flex justify-center items-center min-h-screen p-0 sm:p-4 bg-slate-50">
      {/* Mobile and tablet view (sm and md screens) */}
      <div className="md:hidden flex flex-col w-full sm:max-w-[400px]">
        {/* Background image */}
        <div
          className="w-full overflow-hidden"
          style={backgroundImage ? {
            backgroundImage: `url(${backgroundImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            height: '300px'
          } : {
            backgroundColor: '#5D4037',
            height: '300px'
          }}
        >
          {!backgroundImage && (
            <div className="h-full flex items-center justify-center">
              <div className="text-amber-300 font-bold text-4xl leading-tight text-center">
                Background image here
              </div>
            </div>
          )}
        </div>

        {/* Main container card that holds the other two cards */}
        <Card className="border shadow-md mb-4 overflow-hidden p-4 -mt-3">
          {/* RSVP Status Display for mobile - right after the background image */}
          {invite.response && (
            <div className={`mb-4 p-3 rounded-lg ${invite.status === 'accepted' ? 'border border-green-200 bg-green-50' : 'border border-red-200 bg-red-50'}`}>
              {invite.status === 'accepted' ? (
                <div className="text-green-600">
                  <p>You have RSVPed {invite.response?.adults || invite.adults} {(invite.response?.adults || invite.adults) === 1 ? 'adult' : 'adults'} and {invite.response?.children || invite.children} {(invite.response?.children || invite.children) === 1 ? 'child' : 'children'}</p>
                  {invite.response.timestamp && (
                    <p className="text-green-600">
                      {new Date(invite.response.timestamp).toLocaleDateString()} at {new Date(invite.response.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                    </p>
                  )}
                </div>
              ) : (
                <div className="text-red-600">
                  <p>You have declined this invitation</p>
                  {invite.response.timestamp && (
                    <p className="text-red-600">
                      {new Date(invite.response.timestamp).toLocaleDateString()} at {new Date(invite.response.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                    </p>
                  )}
                </div>
              )}
            </div>
          )}

          {/* First Card: Invite details */}
          <Card className="border border-gray-100 shadow-sm mb-4 overflow-hidden">
            <CardContent className="p-3 sm:p-6">
              {!invite.response && (
                <>
                  <div className="flex justify-center mb-2">
                    <span className="text-4xl">🎉</span>
                  </div>
                  <h3 className="text-xl font-semibold text-center mb-4">You are Invited!</h3>
                </>
              )}

              {/* Greeting */}
              <div className="text-center">
                <p className="text-base font-semibold">
                  <span className="mr-1 text-xl">👋</span> Hey {invite.name.split(' ')[0]},
                </p>
                {event.host && (
                  <p className="text-sm text-gray-600 mb-4">
                    {event.host} has invited you to
                  </p>
                )}

                {/* Event name */}
                {event.eventName && (
                  <h2 className="text-xl font-bold mb-1">
                    {event.eventName}
                  </h2>
                )}

                {/* RSVP Deadline */}
                {event.rsvpDueDate && (
                  <div className="flex justify-center mt-2 mb-1">
                    <div className="inline-flex items-center justify-center px-3 py-0.5 rounded-md bg-gray-50 border border-gray-200">
                      <Hourglass className="h-3 w-3 mr-2 text-gray-700" />
                      <p className="text-xs text-gray-700 font-medium">
                        RSVP Deadline: {new Date(event.rsvpDueDate).toLocaleDateString('en-GB', {day: '2-digit', month: '2-digit', year: 'numeric'}).replace(/\//g, '/')} {new Date(event.rsvpDueDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', hour12: false})}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Second Card: Event details */}
          <Card className="border border-gray-100 shadow-sm mb-4 overflow-hidden">
            <CardContent className="p-3 sm:p-4 space-y-4">
              {event.eventDate && (
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 mr-3 text-gray-500" />
                  <span>{new Date(event.eventDate).toLocaleDateString('en-US', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                  })}</span>
                </div>
              )}
              {event.start && (
                <div className="flex items-center">
                  <Clock className="h-5 w-5 mr-3 text-gray-500" />
                  <span>
                    {event.start.substring(0, 5).split(':').map((n, i) =>
                      i === 0 ? (parseInt(n) > 12 ? (parseInt(n) - 12) + ':' : n + ':') : n
                    ).join('')}
                    {parseInt(event.start.substring(0, 2)) >= 12 ? ' PM' : ' AM'}
                    {event.end ? ' - ' +
                      event.end.substring(0, 5).split(':').map((n, i) =>
                        i === 0 ? (parseInt(n) > 12 ? (parseInt(n) - 12) + ':' : n + ':') : n
                      ).join('') +
                      (parseInt(event.end.substring(0, 2)) >= 12 ? ' PM' : ' AM')
                      : ''}
                    {event.timezone ? ` (${event.timezone})` : ''}
                  </span>
                </div>
              )}
              {event.location && (
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 mr-3 text-gray-500" />
                  <span>{event.location}</span>
                </div>
              )}

              {/* What to Expect section */}
              <div className="pt-2">
                <h3 className="font-semibold mb-2">What to Expect</h3>
                {/* Read more functionality with line clamp */}
                {event.message ? (
                  <div>
                    <p className={`text-sm text-gray-500 ${isExpanded ? '' : 'line-clamp-4'}`}>
                      {event.message}
                    </p>
                    {event.message.length > 150 && (
                      <button
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="text-blue-500 text-sm mt-1 focus:outline-none cursor-pointer"
                      >
                        {isExpanded ? 'show less' : 'read more'}
                      </button>
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">
                    No details yet, Stay tuned!
                  </p>
                )}
              </div>
            </CardContent>
          </Card>



          {/* Event Over Notice for mobile */}
          {isEventOver && event.eventDate && (
            <div className="mb-4 p-4 rounded-lg border border-amber-200 bg-amber-50">
              <p className="font-medium text-amber-800">Event Completed</p>
              <p className="text-sm text-amber-700">
                This event took place on {new Date(event.eventDate).toLocaleDateString()} at {event.start && Format24to12(event.start)} {event.timezone}
              </p>
            </div>
          )}

          {/* RSVP Deadline Notice for mobile */}
          {!isEventOver && isRsvpClosed && event.rsvpDueDate && (
            <div className="mb-4 p-4 rounded-lg border border-red-200 bg-red-50">
              <p className="font-medium text-red-800">RSVP Deadline Passed</p>
              <p className="text-sm text-red-700">
                The deadline for responding to this invitation was {new Date(event.rsvpDueDate).toLocaleDateString()} at {new Date(event.rsvpDueDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})} {event.timezone}
              </p>
            </div>
          )}

          {/* Buttons inside the main card */}
          <div className="flex gap-2 sm:gap-3">
            <Button
              variant="outline"
              onClick={addToCalendar}
              className="flex-1 border-gray-300 h-10"
            >
              <Calendar className="h-4 w-4 mr-2" />
              Add to Calendar
            </Button>
            <Button
              variant="default"
              onClick={() => setRsvpDialogOpen(true)
              }
              className="flex-1 bg-rose-500 hover:bg-rose-500 h-10"
              disabled={isEventOver || isRsvpClosed}
            >
              {invite.response ? "Edit RSVP" : "RSVP Now"}
              {isEventOver && " (Event Over)"}
              {!isEventOver && isRsvpClosed && " (Closed)"}
            </Button>
          </div>
        </Card>
      </div>

      {/* Desktop view (lg and above screens) */}
      <div className="hidden md:flex flex-col md:flex-row max-w-[50rem] w-full gap-2">
        {/* Left card */}
        <div className="w-full md:w-[342px]">
          {/* Event image */}
          <Card className="mb-2 overflow-hidden shadow-[0px_18px_58px_16px_rgba(0,0,0,0.06)] border-0">
            <CardContent className="p-0">
              <div
                className="bg-amber-800 overflow-hidden"
                style={backgroundImage ? {
                  backgroundImage: `url(${backgroundImage})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  height: '342px'
                } : {
                  height: '342px'
                }}
              >
                {!backgroundImage && (
                  <div className="text-yellow-300 font-bold text-2xl leading-tight flex items-center justify-center h-full">
                    Background image here
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Event hosted by section */}
          {event.host && (
            <Card className="border-0">
              <CardHeader className="pb-3 pt-4 px-4 border-b border-[#E2E8F0]">
                <CardTitle className="text-sm font-medium">Event Hosted By</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-2">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-purple-200 flex items-center justify-center mr-2 overflow-hidden">
                    <span className="text-purple-800 font-bold">{hostFirstLetter}</span>
                  </div>
                  <div>
                    <p className="text-sm font-medium">{event.host}</p>
                    <p className="text-xs text-gray-500 flex items-center">
                      <span>{hostEmail}</span>
                      <button
                        className="ml-1 text-gray-400 hover:text-gray-600 focus:outline-none cursor-pointer"
                        onClick={copyEmailToClipboard}
                        aria-label="Copy email to clipboard"
                      >
                        {emailCopied ? <Check size={12} className="text-green-500" /> : <Copy size={12} />}
                      </button>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right card - Invite details */}
        <Card className="flex-1 border-0 overflow-hidden">
          {/* Header with party emoji */}
          <CardHeader className="text-center pt-6 pb-2">
            {!invite.response && (
              <>
                <div className="flex justify-center mb-2">
                  <span className="text-2xl">🎉</span>
                </div>
                <CardTitle className="text-xl">You are Invited!</CardTitle>
              </>
            )}
          </CardHeader>

          {/* Main content */}
          <CardContent className="px-6 py-4">
            {/* Greeting */}
            <div className="mb-4 text-center">
              <p className="text-base font-semibold">
                <span className="mr-1 text-xl">👋</span> Hey {invite.name.split(' ')[0]},
              </p>
              {event.host && (
                <p className="text-sm text-gray-600">
                  {event.host} has invited you to
                </p>
              )}
            </div>

            {/* Event name */}
            {event.eventName && (
              <h2 className="text-2xl font-bold text-center mb-2">
                {event.eventName}
              </h2>
            )}

            {/* RSVP Deadline */}
            {event.rsvpDueDate && (
              <div className="text-center mb-4">
                <div className="inline-flex items-center justify-center px-4 py-1 rounded-md bg-gray-50 border border-gray-200">
                  <Hourglass className="h-3.5 w-3.5 mr-2 text-gray-700" />
                  <p className="text-sm text-gray-700 font-medium">
                    RSVP Deadline: {new Date(event.rsvpDueDate).toLocaleDateString('en-GB', {day: '2-digit', month: '2-digit', year: 'numeric'}).replace(/\//g, '/')} {new Date(event.rsvpDueDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', hour12: false})}
                  </p>
                </div>
              </div>
            )}

            {/* Combined card with event details and What to Expect */}
            <Card className="border border-[#E2E8F0] mb-6 overflow-hidden shadow-none">
              {/* Event details */}
              <CardContent className="p-4 space-y-4">
                {event.eventDate && (
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 mr-3 text-gray-500" />
                    <span>{new Date(event.eventDate).toLocaleDateString('en-US', {
                      day: 'numeric',
                      month: 'long',
                      year: 'numeric'
                    })}</span>
                  </div>
                )}
                {event.start && (
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 mr-3 text-gray-500" />
                    <span>
                      {event.start.substring(0, 5).split(':').map((n, i) =>
                        i === 0 ? (parseInt(n) > 12 ? (parseInt(n) - 12) + ':' : n + ':') : n
                      ).join('')}
                      {parseInt(event.start.substring(0, 2)) >= 12 ? ' PM' : ' AM'}
                      {event.end ? ' - ' +
                        event.end.substring(0, 5).split(':').map((n, i) =>
                          i === 0 ? (parseInt(n) > 12 ? (parseInt(n) - 12) + ':' : n + ':') : n
                        ).join('') +
                        (parseInt(event.end.substring(0, 2)) >= 12 ? ' PM' : ' AM')
                        : ''}
                      {event.timezone ? ` (${event.timezone})` : ''}
                    </span>
                  </div>
                )}
                {event.location && (
                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 mr-3 text-gray-500" />
                    <span>{event.location}</span>
                  </div>
                )}
              </CardContent>

              {/* What to Expect section */}
              <>
                {/* Horizontal line */}
                <Separator className="bg-[#E2E8F0]" />

                <CardContent className="p-4">
                  <h3 className="font-semibold mb-2">What to Expect</h3>
                  {/* Read more functionality with line clamp */}
                  {event.message ? (
                    <div>
                      <p className={`text-sm text-gray-500 ${isExpanded ? '' : 'line-clamp-4'}`}>
                        {event.message}
                      </p>
                      {event.message.length > 150 && (
                        <button
                          onClick={() => setIsExpanded(!isExpanded)}
                          className="text-blue-500 text-sm mt-1 focus:outline-none cursor-pointer"
                        >
                          {isExpanded ? 'show less' : 'read more'}
                        </button>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">
                      No details yet, Stay tuned!
                    </p>
                  )}
                </CardContent>
              </>
            </Card>

            {/* RSVP Status Display */}
            {invite.response && (
              <div className={`mb-4 p-3 rounded-lg ${invite.status === 'accepted' ? 'border border-green-200 bg-green-50' : 'border border-red-200 bg-red-50'}`}>
                {invite.status === 'accepted' ? (
                  <div className="text-green-600">
                    <p>You have RSVPed {invite.response?.adults || invite.adults} {(invite.response?.adults || invite.adults) === 1 ? 'adult' : 'adults'} and {invite.response?.children || invite.children} {(invite.response?.children || invite.children) === 1 ? 'child' : 'children'}</p>
                    {invite.response.timestamp && (
                      <p className="text-green-600">
                        {new Date(invite.response.timestamp).toLocaleDateString()} at {new Date(invite.response.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="text-red-600">
                    <p>You have declined this invitation</p>
                    {invite.response.timestamp && (
                      <p className="text-red-600">
                        {new Date(invite.response.timestamp).toLocaleDateString()} at {new Date(invite.response.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                      </p>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Event Over Notice for desktop */}
            {isEventOver && event.eventDate && (
              <div className="mb-4 p-4 rounded-lg border border-amber-200 bg-amber-50">
                <p className="font-medium text-amber-800">Event Completed</p>
                <p className="text-sm text-amber-700">
                  This event took place on {new Date(event.eventDate).toLocaleDateString()} at {event.start && Format24to12(event.start)} {event.timezone}
                </p>
              </div>
            )}

            {/* RSVP Deadline Notice */}
            {!isEventOver && isRsvpClosed && event.rsvpDueDate && (
              <div className="mb-4 p-4 rounded-lg border border-red-200 bg-red-50">
                <p className="font-medium text-red-800">RSVP Deadline Passed</p>
                <p className="text-sm text-red-700">
                  The deadline for responding to this invitation was {new Date(event.rsvpDueDate).toLocaleDateString()} at {new Date(event.rsvpDueDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})} {event.timezone}
                </p>
              </div>
            )}

            {/* Buttons */}
            <CardFooter className="flex gap-3 p-0">
              <Button
                variant="outline"
                onClick={addToCalendar}
                className="flex-1 border-gray-300 h-10"
              >
                <Calendar className="h-4 w-4 mr-2" />
                Add to Calendar
              </Button>
              <Button
                variant="default"
                onClick={() => setRsvpDialogOpen(true)}
                className="flex-1 bg-rose-500 hover:bg-rose-500 h-10"
                disabled={isEventOver || isRsvpClosed}
              >
                {invite.response ? "Edit RSVP" : "RSVP Now"}
                {isEventOver && " (Event Over)"}
                {!isEventOver && isRsvpClosed && " (Closed)"}
              </Button>
            </CardFooter>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
