import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChevronLeft, Printer } from "lucide-react";
import { useEvent } from "@/hooks/useEvent";
import { useInvites } from "@/hooks/useInvites";
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout";
import { EventInviteListItem } from "@/types";
import { QRCode } from "@/components/ui/qrcode";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { generateRsvpLink } from "@/lib/utils";

type PaperSize = 'A4' | 'A5';

export default function PrintInviteScreen() {
  const router = useRouter();
  const { eventId, inviteId } = router.query;
  const { event, loading: eventLoading } = useEvent(eventId as string);
  const { invites, loading: invitesLoading } = useInvites(eventId as string);
  const [invite, setInvite] = useState<EventInviteListItem | null>(null);
  const [paperSize, setPaperSize] = useState<PaperSize>('A4');

  useEffect(() => {
    if (invites && inviteId) {
      const foundInvite = invites.find((i) => i.ID === inviteId);
      if (foundInvite) {
        setInvite(foundInvite);
      }
    }
  }, [invites, inviteId]);

  const handlePrint = () => {
    window.print();
  };

  const rsvpLink = invite ? generateRsvpLink(invite) : '';

  if (eventLoading || invitesLoading) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </ProtectedLayout>
    );
  }

  if (!event || !invite) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <p className="text-gray-500">Invite not found</p>
        </div>
      </ProtectedLayout>
    );
  }

  return (
    <ProtectedLayout>
      <div className="flex flex-col  bg-gray-50">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-white border-b p-4 flex items-center justify-between no-print">
          <div className="flex items-center">
            <Button 
              variant="ghost" 
              size="icon" 
              className="mr-2" 
              onClick={() => router.push(`/event/${eventId}/invites/${inviteId}`)}
            >
              <ChevronLeft className="h-5 w-5" />
              <span className="sr-only">Back</span>
            </Button>
            <h1 className="text-lg font-semibold">Print Invite</h1>
          </div>
          <div className="flex items-center space-x-4">
            <Select value={paperSize} onValueChange={(value: PaperSize) => setPaperSize(value)}>
              <SelectTrigger className="w-[100px]">
                <SelectValue placeholder="Paper Size" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="A4">A4</SelectItem>
                <SelectItem value="A5">A5</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-4">
          <Card className={`print:shadow-none print:border-none ${paperSize === 'A5' ? 'max-w-[148mm] mx-auto' : 'max-w-[210mm] mx-auto'}`}>
            <CardHeader className="print:py-2">
              <CardTitle className="text-center text-2xl font-bold">{event.eventName}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 print:py-2">
              <div className="text-center space-y-2">
                <p className="text-lg">
                  {event.eventDate && new Date(event.eventDate).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
                <p className="text-lg">
                  {event.start && new Date(event.start).toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                  })}
                  {event.end && ` - ${new Date(event.end).toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                  })}`}
                </p>
                <p className="text-lg">{event.location}</p>
                {event.message && (
                  <p className="text-lg italic">{event.message}</p>
                )}
              </div>

              <div className="text-center space-y-2">
                <p className="text-xl font-semibold">Guest Details</p>
                <p className="text-lg">{invite.name}</p>
                <p className="text-lg">
                  {invite.adults} {invite.adults === 1 ? 'Adult' : 'Adults'}
                  {invite.children > 0 && `, ${invite.children} ${invite.children === 1 ? 'Child' : 'Children'}`}
                </p>
              </div>

              <div className="flex justify-center">
                <QRCode 
                  size={110} 
                  value={rsvpLink} 
                  className="my-[10mm]"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <style jsx global>{`
          @media print {
            @page {
              size: ${paperSize === 'A5' ? 'A5' : 'A4'};
              margin: 0;
            }
            body {
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
            }
            .no-print {
              display: none;
            }
          }
        `}</style>
      </div>
    </ProtectedLayout>
  );
} 