import type React from "react"
import { useState, useRef, useEffect } from "react"
import { useRout<PERSON> } from "next/router"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Upload, AlertCircle, Check, X, AlertTriangle } from "lucide-react"
import CSVTemplateDownload from "@/components/CSVTemplateDownload"
import { useEvent } from "@/hooks/useEvent"
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout"
import { Header } from "@/components/Header"
import { isInviteManagementLocked, getEventLockMessage } from "@/lib/event/eventLock"
import { SendInviteEmailsDialog } from "@/components/SendInviteEmailsDialog"

interface CSVInvite {
  name: string
  group: string
  adults: number
  children: number
  email: string
  phone: string
  isValid: boolean
  error?: string
}

export default function CSVUploadPage() {
  const router = useRouter()
  const { eventId } = router.query
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [invites, setInvites] = useState<CSVInvite[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [importedInvites, setImportedInvites] = useState<any[]>([])
  const [showEmailDialog, setShowEmailDialog] = useState(false)

  const { createBulkInvites, event, loading: eventLoading } = useEvent(eventId as string)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null)
    setSuccess(null)

    const file = e.target.files?.[0]
    if (!file) return

    // Check if it's a CSV file
    if (file.type !== "text/csv" && !file.name.endsWith(".csv")) {
      setError("Please upload a valid CSV file")
      return
    }

    setIsUploading(true)

    const reader = new FileReader()
    reader.onload = (event) => {
      try {
        const csvData = event.target?.result as string
        const parsedInvites = parseCSV(csvData)
        setInvites(parsedInvites)
        setSuccess(`Successfully parsed ${parsedInvites.filter((i) => i.isValid).length} valid invites`)
      } catch (err) {
        setError("Failed to parse CSV file. Please check the format.")
        console.error(err)
      } finally {
        setIsUploading(false)
      }
    }

    reader.onerror = () => {
      setError("Error reading the file")
      setIsUploading(false)
    }

    reader.readAsText(file)
  }

  const parseCSV = (csvData: string): CSVInvite[] => {
    // Split by lines and get headers
    const lines = csvData.split("\n")
    if (lines.length < 2) throw new Error("CSV file must have headers and at least one data row")

    const headers = lines[0].split(",").map((h) => h.trim().toLowerCase())

    // Check required headers
    const requiredHeaders = ["name"]
    const missingHeaders = requiredHeaders.filter((h) => !headers.includes(h))
    if (missingHeaders.length > 0) {
      throw new Error(`Missing required headers: ${missingHeaders.join(", ")}`)
    }

    // Parse data rows
    const parsedInvites: CSVInvite[] = []

    for (let i = 1; i < lines.length; i++) {
      if (!lines[i].trim()) continue // Skip empty lines

      const values = lines[i].split(",").map((v) => v.trim())

      // Create invite object with default values
      const invite: CSVInvite = {
        name: "",
        group: "",
        adults: 0,
        children: 0,
        email: "",
        phone: "",
        isValid: true,
      }

      // Map values to invite object
      headers.forEach((header, index) => {
        if (index >= values.length) return

        switch (header) {
          case "name":
            invite.name = values[index]
            break
          case "group":
            invite.group = values[index]
            break
          case "adults":
            invite.adults = Number.parseInt(values[index]) || 0
            break
          case "children":
            invite.children = Number.parseInt(values[index]) || 0
            break
          case "email":
            invite.email = values[index]
            break
          case "phone":
            invite.phone = values[index]
            break
        }
      })

      // Validate invite
      if (!invite.name) {
        invite.isValid = false
        invite.error = "Name is required"
      }

      parsedInvites.push(invite)    }

    return parsedInvites
  }
  
  const handleCreateInvites = () => {
    const validInvites = invites.filter((invite) => invite.isValid)
    if (validInvites.length === 0) {
      setError("No valid invites to create")
      return
    }    // In a real app, you would send these to your backend
    createBulkInvites(validInvites).then((result) => {
      setSuccess(`Created ${validInvites.length} invites successfully!`);
      
      // Store the imported invites for the email dialog
      if (result && result.invites) {
        setImportedInvites(result.invites);
        setShowEmailDialog(true);
      } else {
        // If we don't get the invites back, redirect to invites list
        router.push(`/event/${eventId}/invites`);
      }
    }).catch((error) => {
      setError("Failed to create invites. Please try again.");
      console.error('Error creating invites:', error);
    });
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  return (
    <ProtectedLayout>
      <div className="flex flex-col  bg-gray-50">
        {/* Use the shared Header component with breadcrumbs */}
        <Header 
          title="Upload CSV"
          breadcrumbs={[
            { label: 'Events', href: '/events' },
            { label: event?.eventName || 'Event Details', href: `/event/${eventId}` },
            { label: 'Invites', href: `/event/${eventId}/invites` }
          ]}
        />

        {/* Content */}
        <div className="flex-1 p-4 pb-20">
          <div className="container mx-auto">
          <Card className="mb-6">
            <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between gap-2 sm:gap-0">
              <div>
                <CardTitle className="text-xl font-semibold mb-2">Add Guest List with a CSV File</CardTitle>
                <p className="text-sm text-gray-500">
                  Easily invite multiple guests at once by uploading a CSV file. Just download our template, fill in the guest details, and upload it here — we&apos;ll take care of the rest.
                </p>
              </div>
              <CSVTemplateDownload />
            </CardHeader>

            {/* Steps Process */}
            <div className="px-6 py-6">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 border border-[#E2E8F0] rounded-[12px] p-4">
                <div className="flex items-start gap-3">
                  <div className="flex justify-center items-center w-[40px] p-[8px_0px] aspect-[1/1] rounded-full bg-[#FEF3EB] text-gray-700 font-medium">1</div>
                  <div>
                    <h3 className="font-medium">Download template</h3>
                    <p className="text-sm text-gray-500">Follow our template format</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex justify-center items-center w-[40px] p-[8px_0px] aspect-[1/1] rounded-full bg-[#FEF3EB] text-gray-700 font-medium">2</div>
                  <div>
                    <h3 className="font-medium">Fill guest details</h3>
                    <p className="text-sm text-gray-500">Use Excel, Google Sheets, etc.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex justify-center items-center w-[40px] p-[8px_0px] aspect-[1/1] rounded-full bg-[#FEF3EB] text-gray-700 font-medium">3</div>
                  <div>
                    <h3 className="font-medium">Upload to create invites</h3>
                    <p className="text-sm text-gray-500">Only CSVs are accepted</p>
                  </div>
                </div>
              </div>
            </div>

            <CardContent>
              <p className="text-sm text-gray-500 mb-4">
                Upload a CSV file with these columns: Name (required), Adults, Children, Email, Phone
              </p>

              <div className="flex items-center justify-center w-full">
                <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <Upload className="w-8 h-8 mb-3 text-gray-500" />
                    <p className="mb-2 text-sm text-gray-500">
                      <span className="font-semibold">Click to upload</span> or drag and drop
                    </p>
                    <p className="text-xs text-gray-500">CSV files only</p>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    className="hidden"
                    accept=".csv"
                    onChange={handleFileChange}
                    disabled={isUploading}
                  />
                </label>
              </div>

              {error && (
                <Alert variant="destructive" className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="mt-4 bg-green-50 text-green-800 border-green-200">
                  <Check className="h-4 w-4" />
                  <AlertTitle>Success</AlertTitle>
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}
            </CardContent>
            <CardFooter>
            </CardFooter>
          </Card>

          {invites.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Parsed Invites</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">Status</TableHead>
                        <TableHead>Name</TableHead>
                        <TableHead>Group</TableHead>
                        <TableHead>Adults</TableHead>
                        <TableHead>Children</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Phone</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {invites.map((invite, index) => (
                        <TableRow key={index} className={!invite.isValid ? "bg-red-50" : ""}>
                          <TableCell>
                            {invite.isValid ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <X className="h-4 w-4 text-red-500" />
                            )}
                          </TableCell>
                          <TableCell className="font-medium">
                            {invite.name}
                            {!invite.isValid && <p className="text-xs text-red-500">{invite.error}</p>}
                          </TableCell>
                          <TableCell>{invite.group}</TableCell>
                          <TableCell>{invite.adults}</TableCell>
                          <TableCell>{invite.children}</TableCell>
                          <TableCell>{invite.email}</TableCell>
                          <TableCell>{invite.phone}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={handleCreateInvites} disabled={!invites.some((i) => i.isValid)} className="w-full cursor-pointer bg-[#F43F5E] hover:bg-[#F43F5E]/90">
                  Create {invites.filter((i) => i.isValid).length} Invites
                </Button>
              </CardFooter>
            </Card>          )}        </div>
      </div> 
    </div>

      {/* Email Dialog */}
      <SendInviteEmailsDialog
        open={showEmailDialog}
        onOpenChange={(open) => {
          setShowEmailDialog(open);
          if (!open) {
            // Redirect to invites list when dialog is closed
            router.push(`/event/${eventId}/invites`);
          }
        }}
        importedInvites={importedInvites}
        eventId={eventId as string}
        importType="bulk"
      />
    </ProtectedLayout>
  )
}