'use client'

import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/router"
import { useState, useEffect } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover"
import { User, Settings, LogOut, Shield, Building2, Users } from "lucide-react"

export function UserProfile() {
  const { data: session } = useSession()
  const router = useRouter()
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
  }

  const handleSignOut = async () => {
    try {
      await signOut({
        redirect: false,
        callbackUrl: "/auth/signin"
      })
      router.push("/auth/signin")
    } catch (error) {
      console.error("Error signing out:", error)
    }
  }

  if (!session) {
    return (
      <Button
        className="!border !border-[#F43F5E] !text-[#F43F5E] cursor-pointer !bg-transparent hover:!bg-transparent hover:!text-[#F43F5E] hover:!border-[#F43F5E]"
        onClick={() => router.push('/auth/signin')}
      >
        Sign In
      </Button>
    )
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full border border-gray-200 shadow-sm hover:shadow hover:border-gray-300 transition-all"
        >
          <Avatar className="h-8 w-8">
            <AvatarImage src={session?.user?.image || undefined} alt={session?.user?.name || "User"} />
            <AvatarFallback>{getInitials(session?.user?.name || "User")}</AvatarFallback>
          </Avatar>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-60 p-2" align="end">
        <div className="flex flex-col space-y-1 p-2">
          <p className="font-medium">{session.user?.name}</p>
          <p className="text-sm text-gray-500 truncate">{session.user?.email}</p>
        </div>
        {session.user?.isAdmin && (<>
          <div className="border-t my-1"></div>
        <div className="flex flex-col space-y-1">
          <Button
            variant="ghost"
            size="sm"
            className="justify-start"
            onClick={() => router.push('/admin/dashboard')}
          >
            <Shield className="mr-2 h-4 w-4" />
            Admin Settings
          </Button>
        </div>
        </>)}

        {/* Partner Panel option - only shown for partner organizations */}
        {session.user?.organization?.type === 'partner' && (<>
          <div className="border-t my-1"></div>
        <div className="flex flex-col space-y-1">
          <Button
            variant="ghost"
            size="sm"
            className="justify-start"
            onClick={() => router.push('/partner')}
          >
            <Building2 className="mr-2 h-4 w-4" />
            Partner Panel
          </Button>
        </div>
        </>)}

        {session.user?.organization?.id && (<>
          <div className="border-t my-1"></div>
        <div className="flex flex-col space-y-1">
          <Button
            variant="ghost"
            size="sm"
            className="justify-start"
            onClick={() => router.push(`/organizations/${session.user.organization?.id}/contact-groups`)}
          >
            <Users className="mr-2 h-4 w-4" />
            Contact Groups
          </Button>
        </div>
        </>)}
        <div className="border-t my-1"></div>
        <div className="flex flex-col space-y-1">
          <Button
            variant="ghost"
            size="sm"
            className="justify-start"
            onClick={() => router.push('/account')}
          >
            <Settings className="mr-2 h-4 w-4" />
            Account Settings
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="justify-start"
            onClick={handleSignOut}
          >
            <LogOut className="mr-2 h-4 w-4" />
            Sign Out
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  )
}