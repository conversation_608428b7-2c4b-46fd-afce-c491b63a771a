/**
 * Prompt templates for GenAI service
 */

import { PromptTemplate, EventContext, GenerationOptions } from './types';

/**
 * Digital invite SVG generation prompt
 */
export const DIGITAL_INVITE_PROMPT: PromptTemplate = {
  system: `You are an expert graphic designer specializing in creating beautiful, modern digital event invitations. Your task is to generate SVG code for digital invitations that will be displayed on mobile and desktop devices.

Key requirements:
- Generate clean, valid SVG code only
- Use modern design principles with good typography
- Ensure text is readable and well-positioned
- Include space for event details
- Use appropriate colors and styling
- Make it visually appealing and professional
- Optimize for both mobile (400x300px) and desktop (800x600px) viewing
- Do not include any explanatory text, only SVG code`,

  user: `Create an SVG digital invitation with the following details:

Event: {{eventName}}
Host: {{host}}
Date: {{eventDate}}
Time: {{startTime}} - {{endTime}}
Location: {{location}}
Message: {{message}}

Design specifications:
- Dimensions: {{width}}x{{height}} pixels
- Theme: {{theme}}
- Color scheme: {{colorScheme}}
- Style: {{style}}

Generate only the SVG code with proper styling, typography, and layout. Make it visually stunning and appropriate for the event type.`,

  examples: [
    {
      input: `Event: Birthday Party, Host: <PERSON>, Date: Dec 25, 2024, Time: 6:00 PM - 10:00 PM, Location: 123 Main St, Theme: fun, Color scheme: vibrant`,
      output: `<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4ecdc4;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#bg)"/>
  <circle cx="150" cy="150" r="30" fill="#ffd93d" opacity="0.8"/>
  <circle cx="650" cy="450" r="40" fill="#ff6b6b" opacity="0.6"/>
  <text x="400" y="150" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="white">🎉 Birthday Party! 🎉</text>
  <text x="400" y="220" font-family="Arial, sans-serif" font-size="24" text-anchor="middle" fill="white">You're Invited by Sarah</text>
  <text x="400" y="300" font-family="Arial, sans-serif" font-size="20" text-anchor="middle" fill="white">December 25, 2024</text>
  <text x="400" y="330" font-family="Arial, sans-serif" font-size="20" text-anchor="middle" fill="white">6:00 PM - 10:00 PM</text>
  <text x="400" y="380" font-family="Arial, sans-serif" font-size="18" text-anchor="middle" fill="white">📍 123 Main St</text>
</svg>`
    }
  ]
};

/**
 * Printable invite SVG generation prompt
 */
export const PRINTABLE_INVITE_PROMPT: PromptTemplate = {
  system: `You are an expert print designer specializing in creating high-quality printable event invitations. Your task is to generate SVG code for invitations that will be printed on paper.

Key requirements:
- Generate clean, valid SVG code optimized for printing
- Use high contrast colors suitable for printing
- Ensure text is crisp and readable when printed
- Include proper margins and spacing
- Use print-safe fonts and colors
- Design for the specified paper size and orientation
- Include bleed areas if necessary
- Make it professional and print-ready
- Do not include any explanatory text, only SVG code`,

  user: `Create a printable SVG invitation with the following details:

Event: {{eventName}}
Host: {{host}}
Date: {{eventDate}}
Time: {{startTime}} - {{endTime}}
Location: {{location}}
Message: {{message}}

Print specifications:
- Paper size: {{paperSize}}
- Orientation: {{orientation}}
- Dimensions: {{width}}x{{height}} pixels (300 DPI)
- Theme: {{theme}}
- Color scheme: {{colorScheme}}

Generate only the SVG code optimized for high-quality printing with proper typography and layout.`,

  examples: [
    {
      input: `Event: Wedding Reception, Host: John & Jane, Date: June 15, 2024, Time: 7:00 PM, Location: Grand Ballroom, Paper: A5, Orientation: portrait, Theme: elegant`,
      output: `<svg width="1748" height="2480" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <rect x="100" y="100" width="1548" height="2280" fill="white" stroke="#e9ecef" stroke-width="2"/>
  <text x="874" y="400" font-family="serif" font-size="72" font-weight="bold" text-anchor="middle" fill="#2c3e50">Wedding Reception</text>
  <line x1="400" y1="450" x2="1348" y2="450" stroke="#bdc3c7" stroke-width="2"/>
  <text x="874" y="600" font-family="serif" font-size="36" text-anchor="middle" fill="#34495e">John & Jane</text>
  <text x="874" y="700" font-family="serif" font-size="28" text-anchor="middle" fill="#7f8c8d">request the pleasure of your company</text>
  <text x="874" y="900" font-family="serif" font-size="32" text-anchor="middle" fill="#2c3e50">June 15, 2024</text>
  <text x="874" y="950" font-family="serif" font-size="32" text-anchor="middle" fill="#2c3e50">7:00 PM</text>
  <text x="874" y="1100" font-family="serif" font-size="28" text-anchor="middle" fill="#34495e">Grand Ballroom</text>
</svg>`
    }
  ]
};

/**
 * Event description generation prompt
 */
export const EVENT_DESCRIPTION_PROMPT: PromptTemplate = {
  system: `You are an expert copywriter specializing in creating compelling event descriptions. Your task is to generate engaging, informative, and well-structured event descriptions that will attract attendees and clearly communicate event details.

Key requirements:
- Write in the specified tone and style
- Include all relevant event information
- Make it engaging and compelling
- Use appropriate length based on requirements
- Include a catchy title if needed
- Focus on benefits and experience for attendees
- Use proper grammar and formatting
- Make it suitable for the target audience`,

  user: `Create an event description with the following details:

Event: {{eventName}}
Host: {{host}}
Date: {{eventDate}}
Time: {{startTime}} - {{endTime}}
Location: {{location}}
Message: {{message}}

Writing specifications:
- Tone: {{tone}}
- Length: {{length}}
- Include details: {{includeDetails}}
- Target audience: General event attendees

Generate a compelling event description that will encourage people to attend.`,

  examples: [
    {
      input: `Event: Tech Conference 2024, Host: TechCorp, Date: March 15, 2024, Time: 9:00 AM - 5:00 PM, Location: Convention Center, Tone: professional, Length: medium`,
      output: `Join us for Tech Conference 2024, the premier technology event of the year! TechCorp is proud to present a full day of cutting-edge insights, networking opportunities, and hands-on workshops.

Discover the latest trends in artificial intelligence, cloud computing, and digital transformation from industry leaders and innovators. Connect with fellow professionals, explore new technologies, and gain valuable insights that will drive your career and business forward.

Event Details:
📅 March 15, 2024
🕘 9:00 AM - 5:00 PM
📍 Convention Center

Don't miss this opportunity to be part of the future of technology. Register now and secure your spot at the most anticipated tech event of the year!`
    }
  ]
};

/**
 * Helper function to replace template variables
 */
export function fillPromptTemplate(
  template: PromptTemplate,
  context: EventContext,
  options: GenerationOptions = {}
): { system: string; user: string } {
  const variables = {
    eventName: context.eventName,
    host: context.host,
    eventDate: context.eventDate.toLocaleDateString(),
    startTime: context.start,
    endTime: context.end,
    location: context.location,
    message: context.message || '',
    width: options.dimensions?.width || 800,
    height: options.dimensions?.height || 600,
    theme: options.theme || 'modern',
    colorScheme: options.colorScheme || 'neutral',
    paperSize: options.paperSize || 'A5',
    orientation: options.orientation || 'portrait',
    tone: options.tone || 'friendly',
    length: options.length || 'medium',
    includeDetails: options.includeDetails ? 'yes' : 'no',
    style: getThemeStyle(options.theme || 'modern'),
  };

  let systemPrompt = template.system;
  let userPrompt = template.user;

  // Replace variables in both system and user prompts
  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `{{${key}}}`;
    systemPrompt = systemPrompt.replace(new RegExp(placeholder, 'g'), String(value));
    userPrompt = userPrompt.replace(new RegExp(placeholder, 'g'), String(value));
  });

  return {
    system: systemPrompt,
    user: userPrompt,
  };
}

/**
 * Get style description for theme
 */
function getThemeStyle(theme: string): string {
  const styles = {
    modern: 'clean, minimalist, geometric shapes, sans-serif fonts',
    classic: 'traditional, elegant, serif typography, timeless design',
    elegant: 'sophisticated, luxurious, ornate details, premium feel',
    fun: 'playful, colorful, rounded shapes, casual and friendly',
    minimal: 'ultra-minimal, black and white, lots of whitespace, simple',
  };

  return styles[theme as keyof typeof styles] || styles.modern;
}

/**
 * Get color palette for color scheme
 */
export function getColorPalette(colorScheme: string): string[] {
  const palettes = {
    warm: ['#dc2626', '#ea580c', '#d97706', '#ca8a04'],
    cool: ['#0284c7', '#0891b2', '#059669', '#16a34a'],
    neutral: ['#374151', '#6b7280', '#9ca3af', '#d1d5db'],
    vibrant: ['#dc2626', '#7c3aed', '#0284c7', '#059669'],
  };

  return palettes[colorScheme as keyof typeof palettes] || palettes.neutral;
}
