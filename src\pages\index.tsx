'use client'

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/router";
import Image from "next/image";
import { ButtonVariant, Header } from "@/components/Header";
import { useSession } from "next-auth/react";
import { Footer } from "@/components/Footer";
import { Card, CardContent } from "@/components/ui/card";
import { useEffect } from "react";

export default function Index() {
  const router = useRouter()
  const { data: session, status } = useSession()

  // No redirection for authenticated users - they can view the home page

  // Configure header buttons for home page
  const headerButtons = session ? [
    {
      label: "Manage Events",
      onClick: () => router.push('/events'),
      variant: "outline" as ButtonVariant
    }
  ] : [];

  // Event types with icons
  const eventTypes = [
    { name: "Organize a birthday", icon: "/HostMoments/birthday.svg" },
    { name: "Graduation Party", icon: "/HostMoments/graduation.svg" },
    { name: "Housewarming Party", icon: "/HostMoments/housewarming.svg" },
    { name: "Baby Shower", icon: "/HostMoments/babyshower.svg" },
    { name: "Dinner Parties", icon: "/HostMoments/dinner.svg" },
    { name: "Farewell Party", icon: "/HostMoments/farewell.svg" },
    { name: "Custom Event", icon: "/HostMoments/custom.svg" }
  ];

  // Features
  const features = [
    {
      title: "Easy Event Creation",
      description: "Create your first event in minutes with our intuitive interface.",
      icon: "/OurFeatures/easy-event.svg"
    },
    {
      title: "Smart RSVP Management",
      description: "Track responses, manage guests, and send email invites.",
      icon: "/OurFeatures/rsvp.svg"
    },
    {
      title: "Multiple Ways to Invite",
      description: "Share invites via email, QR codes, or drop a link to your guest list.",
      icon: "/OurFeatures/multiple-ways.svg"
    },
    {
      title: "Real-time Updates",
      description: "Get notified live whenever someone RSVPs, cancels, or leaves a note.",
      icon: "/OurFeatures/realtime-updates.svg"
    }
  ];

  return (
    <div className="flex flex-col min-h-screen">
      {/* Fixed header at the top */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm shadow-sm">
        <Header buttons={headerButtons} className="border-none" />
      </div>

      {/* Hero Section with Video Background - Full Height */}
      <div className="relative min-h-screen flex items-center overflow-hidden">
        {/* Background image (fallback) */}
        <div className="absolute inset-0">
          <Image
            src="/bgImage.jpeg"
            alt="Event background"
            fill
            className="object-cover"
            priority
          />
        </div>

        {/* Video background */}
        <div className="absolute inset-0 w-full h-full">
          <video
            src="/heroVideo.mp4"
            autoPlay
            muted
            loop
            playsInline
            className="absolute w-full h-full object-cover"
          />
        </div>

        {/* Radial gradient overlay */}
        <div
          className="absolute inset-0"
          style={{
            background: "radial-gradient(50% 50% at 50% 50%, rgba(154, 145, 148, 0.20) 0%, rgba(30, 19, 21, 0.40) 100%)"
          }}
        ></div>

        {/* Content */}
        <div className="relative z-10 container mx-auto px-4 text-center text-white py-16 pt-24">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">Welcome to Effortless<br />Event Hosting</h1>
          <p className="text-xl md:text-2xl mb-10 max-w-2xl mx-auto">I am coming (IaC) is an online event RSVP management platform designed for smaller events.</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center w-full max-w-md mx-auto">            <Button
              variant="primary-button"
              size="lg"
              className="flex justify-center items-center w-full sm:w-auto sm:min-w-[160px] py-5 px-6 gap-1 cursor-pointer rounded-md border-2 border-transparent"
              onClick={() => router.push('/event/new/edit')}
            >
              Create New Event
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="flex justify-center items-center w-full sm:w-auto sm:min-w-[160px] py-5 px-6 gap-1 cursor-pointer rounded-md border-2 border-[#F43F5E] bg-[rgba(139,139,139,0.2)] text-white hover:bg-white hover:text-gray-900 transition-colors duration-300"
              onClick={() => router.push('/pricing')}
            >
              View Pricing
            </Button>
          </div>
        </div>
      </div>

      {/* Event Types Section */}
      <section className="py-20 relative overflow-hidden bg-[#FFF3EB]">
        {/* Background SVG */}
        <div className="absolute inset-0 w-full h-full">
          <Image
            src="/HostMomentsBackground.svg"
            alt="Background pattern"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-6xl mx-auto">
            {/* Header content - centered on all screens */}
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Host moments that matter effortlessly</h2>
              <p className="text-gray-600 text-lg mb-8 max-w-2xl mx-auto">
                Small get-togethers, big memories. I am coming is perfect for
                personal events where timing and presence count
              </p>
            </div>

            {/* Event Type Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              {/* First row - 4 cards (2 per row on small screens, 4 on large) */}
              <Card className="text-center border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 rounded-xl overflow-hidden bg-white">
                <CardContent className="p-6">
                  <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center bg-[#FFF3EB] rounded-full p-3">
                    <Image src={eventTypes[0].icon} alt={eventTypes[0].name} width={40} height={40} />
                  </div>
                  <h3 className="font-medium text-gray-800">{eventTypes[0].name}</h3>
                </CardContent>
              </Card>
              <Card className="text-center border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 rounded-xl overflow-hidden bg-white">
                <CardContent className="p-6">
                  <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center bg-[#FFF3EB] rounded-full p-3">
                    <Image src={eventTypes[1].icon} alt={eventTypes[1].name} width={40} height={40} />
                  </div>
                  <h3 className="font-medium text-gray-800">{eventTypes[1].name}</h3>
                </CardContent>
              </Card>
              <Card className="text-center border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 rounded-xl overflow-hidden bg-white">
                <CardContent className="p-6">
                  <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center bg-[#FFF3EB] rounded-full p-3">
                    <Image src={eventTypes[2].icon} alt={eventTypes[2].name} width={40} height={40} />
                  </div>
                  <h3 className="font-medium text-gray-800">{eventTypes[2].name}</h3>
                </CardContent>
              </Card>
              <Card className="text-center border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 rounded-xl overflow-hidden bg-white">
                <CardContent className="p-6">
                  <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center bg-[#FFF3EB] rounded-full p-3">
                    <Image src={eventTypes[3].icon} alt={eventTypes[3].name} width={40} height={40} />
                  </div>
                  <h3 className="font-medium text-gray-800">{eventTypes[3].name}</h3>
                </CardContent>
              </Card>
            </div>

            {/* Second row - 3 cards (centered) */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-3xl mx-auto">
              <Card className="text-center border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 rounded-xl overflow-hidden bg-white">
                <CardContent className="p-6">
                  <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center bg-[#FFF3EB] rounded-full p-3">
                    <Image src={eventTypes[4].icon} alt={eventTypes[4].name} width={40} height={40} />
                  </div>
                  <h3 className="font-medium text-gray-800">{eventTypes[4].name}</h3>
                </CardContent>
              </Card>
              <Card className="text-center border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 rounded-xl overflow-hidden bg-white">
                <CardContent className="p-6">
                  <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center bg-[#FFF3EB] rounded-full p-3">
                    <Image src={eventTypes[5].icon} alt={eventTypes[5].name} width={40} height={40} />
                  </div>
                  <h3 className="font-medium text-gray-800">{eventTypes[5].name}</h3>
                </CardContent>
              </Card>
              <Card className="text-center border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 rounded-xl overflow-hidden bg-white">
                <CardContent className="p-6">
                  <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center bg-[#FFF3EB] rounded-full p-3">
                    <Image src={eventTypes[6].icon} alt={eventTypes[6].name} width={40} height={40} />
                  </div>
                  <h3 className="font-medium text-gray-800">{eventTypes[6].name}</h3>
                </CardContent>
              </Card>
            </div>

            {/* Create Event Button - centered */}
            <div className="text-center mt-10">
              <Button
                variant="primary-button"
                onClick={() => router.push('/event/new/edit')}
                className="py-3 px-8 text-base transition-colors duration-300 rounded-md"
              >
                Create new Event
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl font-bold mb-6">Our Features that Make Hosting Effortless</h2>
            <p className="text-gray-600 text-lg">Let us handle the logistics so you can focus on creating amazing memories.</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 max-w-6xl mx-auto">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-24 h-24 mx-auto mb-6 flex items-center justify-center  rounded-full p-5">
                  <Image src={feature.icon || "/icon.svg"} alt={feature.title} width={60} height={60} className="text-pink-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* What Hosts and Guests Love Section */}
      <section className="py-24 border-t border-gray-200 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl font-bold mb-6">What you and your guests will love</h2>
            <p className="text-gray-600 text-lg">I am coming is perfect for personal events where timing and presence count!</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-5 max-w-5xl mx-auto">
            {/* For Hosts */}
            <div className="bg-[#FDF6F0] p-10 rounded-sm shadow-lg relative overflow-hidden border border-gray-100" style={{ height: 296 }}>
              <div className="absolute top-0 right-0 w-40 h-40 mt-[60px] -mr-6 hidden sm:block">
                <Image src="/GuestsHostsIllustration/Illustration-hosts.svg" alt="Hosts" width={160} height={160} />
              </div>
              <h3 className="text-xl font-semibold mb-8 flex items-center">
                <Image src="/GuestsHostsIllustration/mic.svg" alt="Mic icon" width={30} height={30} className="mr-2" />
                <span className="py-2 px-4 rounded-full mr-3 font-bold">For Hosts</span>
              </h3>
              <ul className="space-y-1">
                <li className="flex items-start">
                  <div className="text-green-500 mr-3 mt-1 text-lg font-bold">✓</div>
                  <span className="text-gray-700">Track RSVPs in real time</span>
                </li>
                <li className="flex items-start">
                  <div className="text-green-500 mr-3 mt-1 text-lg font-bold">✓</div>
                  <span className="text-gray-700">Send QR code email/SMS/WA invites</span>
                </li>
                <li className="flex items-start">
                  <div className="text-green-500 mr-3 mt-1 text-lg font-bold">✓</div>
                  <span className="text-gray-700">Export guest list with RSVP data</span>
                </li>
                <li className="flex items-start">
                  <div className="text-green-500 mr-3 mt-1 text-lg font-bold">✓</div>
                  <span className="text-gray-700">Create beautiful event pages</span>
                </li>
              </ul>
            </div>

            {/* For Guests */}
            <div className="bg-white p-10 rounded-sm shadow-lg relative overflow-hidden border border-gray-100" style={{ height: 296 }}>
              <div className="absolute top-0 right-0 w-40 h-40 mt-[60px] -mr-6 hidden sm:block">
                <Image src="/GuestsHostsIllustration/Illustration-guests.svg" alt="Guests" width={160} height={160} />
              </div>
              <h3 className="text-xl font-semibold mb-8 flex items-center">
                <Image src="/GuestsHostsIllustration/for-guests.svg" alt="Guest icon" width={40} height={40} className="mr-2" />
                <span className="py-2 px-4 rounded-full mr-3 font-bold">For Guests</span>
              </h3>
              <ul className="space-y-1">
                <li className="flex items-start">
                  <div className="text-green-500 mr-3 mt-1 text-lg font-bold">✓</div>
                  <span className="text-gray-700">Personalized digital invites</span>
                </li>
                <li className="flex items-start">
                  <div className="text-green-500 mr-3 mt-1 text-lg font-bold">✓</div>
                  <span className="text-gray-700">Save invites to mobile wallets</span>
                </li>
                <li className="flex items-start">
                  <div className="text-green-500 mr-3 mt-1 text-lg font-bold">✓</div>
                  <span className="text-gray-700">Scan QR codes for quick check-in</span>
                </li>
                <li className="flex items-start">
                  <div className="text-green-500 mr-3 mt-1 text-lg font-bold">✓</div>
                  <span className="text-gray-700">Get instant event updates</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-[#F43F5E] text-white">
        <div className="container mx-auto px-4 text-center max-w-4xl">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Create beautiful invites, manage guests with ease, and make every RSVP count</h2>
          <p className="mb-10 text-lg md:text-xl opacity-90">Start planning your coming event with I am Coming.</p>
          <Button
            size="lg"
            className="py-6 px-10 text-lg bg-white text-[#F43F5E] cursor-pointer hover:bg-gray-100 shadow-lg hover:shadow-xl transition-all duration-300"
            onClick={() => router.push('/event/new/edit')}
          >
            Create Your Event Now
          </Button>
        </div>
      </section>

      <Footer type="marketing" />
    </div>
  )
}