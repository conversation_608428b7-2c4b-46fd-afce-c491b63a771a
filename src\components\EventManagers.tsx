import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Trash2, Plus, LockIcon, LogOut, Loader2 } from "lucide-react"
import { z } from "zod"
import { debugLog, log } from "@/lib/logger"
import { useSession } from "next-auth/react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useEventManagers } from "@/hooks/useEventManagers"
import { EventManager } from "@/types"
import Image from "next/image"

const emailSchema = z.string().email("Please enter a valid email address")

interface EventManagersProps {
  eventId: string;
  managers: string[];  // Array of account IDs or emails
  onUpdate: (managers: string[]) => void;
  isManager?: boolean; // Added to indicate if current user is a manager
}

function ManagerCard({ manager, onRemove }: { manager: EventManager; onRemove: () => void }) {
  return (
    <div className="flex items-center justify-between gap-2 p-2 bg-gray-50 rounded-md">
      <div className="flex items-center">
        {manager.image ? (
          <img
            src={manager.image}
            alt={manager.name || manager.email}
            className="h-8 w-8 rounded-full mr-2"
          />
        ) : (
          <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-2">
            <span className="text-gray-500">{manager.email.charAt(0).toUpperCase()}</span>
          </div>
        )}
      </div>
      <div className="flex flex-1 flex-col">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">{manager.name || manager.email}</span>
          {manager.role === 'invited' && (
            <Badge variant="outline" className="text-xs bg-amber-50 text-amber-600 border-amber-200">
              Invited
            </Badge>
          )}
        </div>
        {manager.name && <span className="text-xs text-gray-500">{manager.email}</span>}
      </div>
      <Button variant="ghost" size="icon" onClick={onRemove} className="h-8 w-8">
        <Trash2 className="h-4 w-4 text-red-500" />
      </Button>
    </div>
  )
}
export default function EventManagers({ eventId, managers: managersList, onUpdate, isManager = false }: EventManagersProps) {
  const [newEmail, setNewEmail] = useState("")
  const [error, setError] = useState<string | null>(null)
  const { data: session } = useSession()
  const [removeSelfDialogOpen, setRemoveSelfDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { managers } = useEventManagers(managersList)

  debugLog('EventManagers', {
    eventId,
    managers,
    isManager
  })

  const handleAddManager = async () => {
    try {
      // Validate email
      emailSchema.parse(newEmail)
      setError(null)

      setIsLoading(true);

      // Look up user ID by email
      try {
        const response = await fetch('/api/users/lookup-by-email', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: newEmail })
        });

        if (response.ok) {
          const data = await response.json();

          if (data.user) {
            // User exists in the system
            const userId = data.user.id;

            // Check if user is already a manager
            if (managers.some(m => m.userId === userId)) {
              setError("This user is already added as a manager");
              setIsLoading(false);
              return;
            }

            // Add new manager by ID
            const updatedManagerIds = [...managers.map(m => m.userId || m.email), userId];
            onUpdate(updatedManagerIds);
          } else {
            // User doesn't exist - add by email and send invitation
            // Check if email is already in the managers list
            if (managers.some(m => m.email === newEmail)) {
              setError("This email is already added as a manager");
              setIsLoading(false);
              return;
            }

            // Add the email to managers array
            const updatedManagerIds = [...managers.map(m => m.userId || m.email), newEmail];

            // Send invitation email to the new manager
            try {
              const response = await fetch(`/api/event/${eventId}/managers/invite`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: newEmail })
              });

              const result = await response.json();

              // If email wasn't sent but manager was added, show a message
              if (response.ok && result.success && result.emailSent === false) {
                console.log("Manager added but email could not be sent:", result.message);
                // We could show a toast notification here if desired
              }
            } catch (error) {
              console.error("Error sending manager invitation:", error);
              // Continue even if email sending fails
            }

            onUpdate(updatedManagerIds);
          }

          setNewEmail("");
        } else {
          setError("Failed to look up user");
        }
      } catch (err) {
        setError("An error occurred while adding manager");
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    } catch (err) {
      if (err instanceof z.ZodError) {
        setError(err.errors[0].message);
      }
    }
  }

  const handleRemoveManager = async (identifier?: string, isEmail: boolean = false) => {
    if (!identifier) return;

    let updatedManagerIds;
    if (isEmail) {
      // Filter by email for invited managers
      updatedManagerIds = managers
        .filter(m => m.email !== identifier)
        .map(m => m.userId || m.email);
    } else {
      // Filter by userId for existing managers
      updatedManagerIds = managers
        .filter(m => m.userId !== identifier)
        .map(m => m.userId || m.email);
    }

    onUpdate(updatedManagerIds);
  }

  const handleRemoveSelf = async () => {
    if (!session?.user?.id) return;
    const updatedManagerIds = managers
      .filter(m => m.userId !== session.user.id)
      .map(m => m.userId || m.email);
    onUpdate(updatedManagerIds);
    setRemoveSelfDialogOpen(false);
  }

  // If the user is only a manager (not owner), show a read-only view with a remove self option
  if (isManager) {
    return (
      <>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>Event Managers</span>
              <LockIcon className="h-4 w-4 text-gray-400" />
              <span className="text-sm font-normal text-gray-500">(Read only)</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Current Managers</Label>
              <div className="space-y-2">
                {isLoading ? (
                  <div className="flex items-center justify-center p-4">
                    <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
                  </div>
                ) : managers.length === 0 ? (
                  <p className="text-sm text-gray-500">No managers added yet</p>
                ) : (
                  managers.map((managerInfo) => {
                    const isInvited = managerInfo.role === 'invited';
                    const identifier = isInvited ? managerInfo.email : managerInfo.userId;
                    return <ManagerCard
                      key={identifier || managerInfo.email}
                      manager={managerInfo}
                      onRemove={() => handleRemoveManager(identifier, isInvited)}
                    />
                  })
                )}
              </div>
            </div>

            {/* Add a button for the manager to remove themselves */}
            <div className="pt-2">
              <Button
                variant="outline"
                className="w-full text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
                onClick={() => setRemoveSelfDialogOpen(true)}
              >
                <LogOut className="h-4 w-4 mr-2" />
                Remove myself as manager
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Confirmation dialog for removing self as manager */}
        <AlertDialog open={removeSelfDialogOpen} onOpenChange={setRemoveSelfDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Remove yourself as manager?</AlertDialogTitle>
              <AlertDialogDescription>
                You will no longer have access to manage this event. This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleRemoveSelf}
                className="bg-red-600 hover:bg-red-700"
              >
                Remove Myself
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    )
  }

  // Regular editable view for event owners
  return (
    <Card>
      <CardHeader>
        <CardTitle>Event Managers</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">        <div className="space-y-2">
          <Label>Add Manager</Label>
          <div className="flex gap-2">
            <Input
              type="email"
              placeholder="Enter email address"
              value={newEmail}
              onChange={(e) => setNewEmail(e.target.value)}
              className="flex-1"
              disabled={isLoading}
            />
            <Button
              onClick={handleAddManager}
              disabled={!newEmail || isLoading}
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            Add an Event Manager to take care of everything on your behalf.
          </p>
          {error && <p className="text-sm text-red-500">{error}</p>}
        </div>

        <div className="space-y-2">
          <Label>Current Managers</Label>
          <div className="space-y-2">
            {isLoading && managers.length === 0 ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
              </div>
            ) : managers.length === 0 ? (
              <p className="text-sm text-muted-foreground">No managers added yet</p>
            ) : (
              managers.map((managerInfo) => {
                const isInvited = managerInfo.role === 'invited';
                const identifier = isInvited ? managerInfo.email : managerInfo.userId;
                return <ManagerCard
                  key={identifier || managerInfo.email}
                  manager={managerInfo}
                  onRemove={() => handleRemoveManager(identifier, isInvited)}
                />
              })
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}