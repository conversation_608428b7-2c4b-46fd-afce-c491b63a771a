import React, { useState } from 'react';
import type { <PERSON>a, StoryObj } from '@storybook/react';
import RSVPDialog from './RSVPDialog';
import { Event, EventInvite } from '@/types';

// Create a proper Story meta object
const meta = {
  title: 'Components/RSVPDialog',
  component: RSVPDialog,
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof RSVPDialog>;

export default meta;
type Story = StoryObj<typeof RSVPDialog>;

// Mock event and invite data
const mockEvent:Event = {
  ID: 'event-123',
  eventName: 'Birthday Celebration',
  timezone: 'America/New_York',
  eventDate: new Date('2025-05-12'),
  start: '18:00',
  end: '21:00',
  location: '123 Party Lane',
  message: 'Join us for a memorable evening!',
  host: '<PERSON>',
  ownerEmail: '<EMAIL>',
  ownerAccountId: 'owner-123',
  organizationId: 'org-456',
  managers: ['<EMAIL>', '<EMAIL>']
};

const mockInvite: EventInvite = {
  ID: 'invite-123',
  status: 'invited' as 'invited' | 'accepted' | 'declined',
  adults: 2,
  children: 1,
  email: '<EMAIL>',
  phone: '************',
  response: null,
  name: 'John Doe', // Added missing property
  eventId: 'event-456', // Added missing property
  message: [{
    id: 'msg-001',
    sender: 'host', // Updated sender to match allowed values
    content: 'Looking forward to the event!',
    timestamp: new Date()
  }] // Added required 'id' and 'sender' properties to match Message type
};

// Create a mock version of the RSVPDialog that doesn't depend on the hook
// This is necessary because we can't easily mock modules in Storybook
const MockRSVPDialog = (props: any) => {
  const [status, setStatus] = useState('');
  const [guests, setGuests] = useState({
    adults: props.invite.adults || 0,
    children: props.invite.children || 0
  });
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Mock the submission process
    setTimeout(() => {
      const response = {
        status,
        adults: guests.adults,
        children: guests.children,
        message
      };
      
      props.onRSVPResponded && props.onRSVPResponded(status, response);
      setIsSubmitting(false);
    }, 1000);
  };

  if (!props.rsvpDialogOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-semibold mb-4">RSVP for {props.event.eventName}</h2>
        
        <form onSubmit={handleSubmit}>
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium mb-1">Will you attend?</label>
              <div className="flex space-x-4">
                <button
                  type="button"
                  className={`px-4 py-2 border rounded-md ${status === 'accepted' ? 'bg-green-100 border-green-500' : 'bg-white'}`}
                  onClick={() => setStatus('accepted')}
                >
                  Yes, I&apos;ll be there
                </button>
                <button
                  type="button"
                  className={`px-4 py-2 border rounded-md ${status === 'declined' ? 'bg-red-100 border-red-500' : 'bg-white'}`}
                  onClick={() => setStatus('declined')}
                >
                  No, I can&apos;t make it
                </button>
              </div>
            </div>
            
            {status === 'accepted' && (
              <>
                <div>
                  <label className="block text-sm font-medium mb-1">Number of guests</label>
                  <div className="flex items-center space-x-4">
                    <div>
                      <label className="text-xs block" htmlFor="adults-input">Adults</label>
                      <input
                        id="adults-input"
                        title="Adults"
                        type="number"
                        min="1"
                        max="10"
                        value={guests.adults}
                        onChange={(e) => setGuests({...guests, adults: parseInt(e.target.value)})}
                        className="border rounded p-2 w-20"
                        aria-label='Number of adults'
                      />
                      <label className="text-xs block" htmlFor="children-input">Children</label>
                      <input
                        id="children-input"
                        title="Children"
                        type="number"
                        min="0"
                        max="10"
                        value={guests.children}
                        onChange={(e) => setGuests({...guests, children: parseInt(e.target.value)})}
                        className="border rounded p-2 w-20"
                        aria-label='Number of children'
                      />
                    </div>
                  </div>
                </div>
              </>
            )}
            
            <div>
              <label className="block text-sm font-medium mb-1">Message (optional)</label>
              <textarea
                className="border rounded-md p-2 w-full"
                rows={3}
                placeholder="Any dietary restrictions or special requests?"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
              />
            </div>
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              className="px-4 py-2 border rounded-md"
              onClick={() => props.setRsvpDialogOpen(false)}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!status || isSubmitting}
              className={`px-4 py-2 rounded-md ${!status || isSubmitting ? 'bg-gray-300 text-gray-500' : 'bg-blue-500 text-white'}`}
            >
              {isSubmitting ? 'Submitting...' : 'Submit RSVP'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Create a template that handles the dialog state using our mock component
const RSVPDialogTemplate = (args: any) => {
  const [isOpen, setIsOpen] = useState(true);
  const [response, setResponse] = useState<any>(null);
  
  const handleRSVPResponded = (status: string, responseData: any) => {
    console.log('RSVP responded:', status, responseData);
    setResponse({
      ...responseData,
      status
    });
    setTimeout(() => setIsOpen(false), 1000);
  };
  
  return (
    <div className="flex flex-col items-center gap-4">
      <button 
        className="px-4 py-2 bg-blue-500 text-white rounded"
        onClick={() => setIsOpen(true)}
      >
        Open RSVP Dialog
      </button>
      
      {response && (
        <div className="p-4 border rounded bg-gray-50 max-w-md">
          <h3 className="font-bold mb-2">Last Response:</h3>
          <pre className="text-xs overflow-auto">{JSON.stringify(response, null, 2)}</pre>
        </div>
      )}
      
      {/* Use our mock component instead of the real one that requires the hook */}
      <MockRSVPDialog
        {...args}
        rsvpDialogOpen={isOpen}
        setRsvpDialogOpen={setIsOpen}
        onRSVPResponded={handleRSVPResponded}
      />
    </div>
  );
};

export const Default: Story = {
  render: (args) => <RSVPDialogTemplate {...args} />,
  args: {
    event: mockEvent,
    invite: mockInvite,
  },
};

export const WithExistingResponse: Story = {
  render: (args) => <RSVPDialogTemplate {...args} />,
  args: {
    event: mockEvent,
    invite: {
      ...mockInvite,
      status: 'accepted',
      response: {
        adults: 2,
        children: 1,
        message: "I'm looking forward to the event! I have a gluten allergy.",
        timestamp: new Date().toISOString(),
      },
    },
  },
};

export const DeclinedResponse: Story = {
  render: (args) => <RSVPDialogTemplate {...args} />,
  args: {
    event: mockEvent,
    invite: {
      ...mockInvite,
      status: 'declined',
      response: {
        adults: 0,
        children: 0,
        message: "Sorry, I can't make it this time.",
        timestamp: new Date().toISOString(),
      },
    },
  },
};