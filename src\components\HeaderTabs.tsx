import { cn } from "@/lib/utils";
import { useState } from "react";

interface HeaderTabsProps {
  tabs: Array<{
    label: string;
    id: string;
  }>;
  selected?: string;
  onChange?: (tabId: string) => void;
}

export function HeaderTabs({ tabs, selected, onChange }: HeaderTabsProps) {
  const [selectedTab, setSelectedTab] = useState(selected || tabs.find(tab => tab.id === "digital-card")?.id || tabs[0].id);

  const handleTabChange = (tabId: string) => {
    setSelectedTab(tabId);
    if (onChange) {
      onChange(tabId);
    }
  };

  return <nav className="bg-gray-100">
    <ul className="flex">
      {tabs.map((tab) => (
        <li key={tab.id} className={cn("block px-4 lg:px-8 py-3 border-[var(--primary-button)]", selectedTab === tab.id ? "border-b-2" : "")}>
          <a
            onClick={()=> handleTabChange(tab.id)}
            href={`#${tab.id}`}
            className={cn(
              "text-sm font-normal",
              selectedTab === tab.id ? "text-black" : "text-muted-foreground"
            )}
          >
            {tab.label}
          </a>
        </li>
      ))}
    </ul>
  </nav>
}