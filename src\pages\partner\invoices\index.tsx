'use client'

import { useState } from "react"
import { PartnerLayout } from "@/components/layouts/PartnerLayout"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/router"
import { CreditCard, Download, Eye, FileText, Filter } from "lucide-react"
import { useSession } from "next-auth/react"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

// Mock data for invoices
const mockInvoices = [
  {
    id: "inv-001",
    period: {
      start: new Date(2023, 10, 1),
      end: new Date(2023, 10, 30)
    },
    events: ["event-1", "event-2", "event-3"],
    amount: 105.00,
    status: "paid",
    paymentMethod: "card",
    paymentDate: new Date(2023, 11, 2)
  },
  {
    id: "inv-002",
    period: {
      start: new Date(2023, 11, 1),
      end: new Date(2023, 11, 31)
    },
    events: ["event-4", "event-5"],
    amount: 70.00,
    status: "pending"
  },
  {
    id: "inv-003",
    period: {
      start: new Date(2023, 9, 1),
      end: new Date(2023, 9, 31)
    },
    events: ["event-6"],
    amount: 35.00,
    status: "paid",
    paymentMethod: "bank_transfer",
    paymentDate: new Date(2023, 10, 5)
  }
];

export default function PartnerInvoices() {
  const router = useRouter()
  const { data: session } = useSession()
  const [invoices, setInvoices] = useState(mockInvoices)
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null)
  const [isViewInvoiceOpen, setIsViewInvoiceOpen] = useState(false)
  const [filterStatus, setFilterStatus] = useState<string>("all")
  
  // Filter invoices based on status
  const filteredInvoices = filterStatus === "all" 
    ? invoices 
    : invoices.filter(invoice => invoice.status === filterStatus);
  
  const handleViewInvoice = (invoice: any) => {
    setSelectedInvoice(invoice);
    setIsViewInvoiceOpen(true);
  };
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "paid":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Paid</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Pending</Badge>;
      case "overdue":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Overdue</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <PartnerLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Invoices</h1>
            <p className="text-gray-500">View and manage your invoices</p>
          </div>
          <div className="flex items-center gap-2">
            <Label htmlFor="filter-status" className="sr-only">Filter by status</Label>
            <Select 
              value={filterStatus} 
              onValueChange={setFilterStatus}
            >
              <SelectTrigger id="filter-status" className="w-[180px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Invoices</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="overdue">Overdue</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* Invoices Table */}
        <Card>
          <CardHeader>
            <CardTitle>Invoice History</CardTitle>
            <CardDescription>
              View your past and current invoices
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredInvoices.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No invoices found.</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice #</TableHead>
                    <TableHead>Period</TableHead>
                    <TableHead>Events</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredInvoices.map((invoice) => (
                    <TableRow key={invoice.id}>
                      <TableCell className="font-medium">{invoice.id}</TableCell>
                      <TableCell>
                        {format(invoice.period.start, "MMM d, yyyy")} - {format(invoice.period.end, "MMM d, yyyy")}
                      </TableCell>
                      <TableCell>{invoice.events.length} events</TableCell>
                      <TableCell>{formatCurrency(invoice.amount)}</TableCell>
                      <TableCell>{getStatusBadge(invoice.status)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleViewInvoice(invoice)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
        
        {/* Invoice Details Dialog */}
        <Dialog open={isViewInvoiceOpen} onOpenChange={setIsViewInvoiceOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Invoice #{selectedInvoice?.id}</DialogTitle>
              <DialogDescription>
                Invoice details for period {selectedInvoice && format(selectedInvoice.period.start, "MMMM d, yyyy")} - {selectedInvoice && format(selectedInvoice.period.end, "MMMM d, yyyy")}
              </DialogDescription>
            </DialogHeader>
            
            {selectedInvoice && (
              <div className="space-y-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold text-lg">I am Coming</h3>
                    <p className="text-sm text-gray-500">123 Event Street</p>
                    <p className="text-sm text-gray-500">Melbourne, VIC 3000</p>
                    <p className="text-sm text-gray-500">Australia</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">Invoice Date: {format(new Date(), "MMMM d, yyyy")}</p>
                    <p className="text-sm text-gray-500">Due Date: {format(new Date(new Date().setDate(new Date().getDate() + 30)), "MMMM d, yyyy")}</p>
                    <div className="mt-2">
                      {getStatusBadge(selectedInvoice.status)}
                    </div>
                  </div>
                </div>
                
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Description</TableHead>
                        <TableHead className="text-right">Quantity</TableHead>
                        <TableHead className="text-right">Unit Price</TableHead>
                        <TableHead className="text-right">Amount</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>Event Creation Fee</TableCell>
                        <TableCell className="text-right">{selectedInvoice.events.length}</TableCell>
                        <TableCell className="text-right">$35.00</TableCell>
                        <TableCell className="text-right">{formatCurrency(selectedInvoice.amount)}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell colSpan={3} className="text-right font-medium">Subtotal</TableCell>
                        <TableCell className="text-right">{formatCurrency(selectedInvoice.amount)}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell colSpan={3} className="text-right font-medium">Tax (0%)</TableCell>
                        <TableCell className="text-right">$0.00</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell colSpan={3} className="text-right font-bold">Total</TableCell>
                        <TableCell className="text-right font-bold">{formatCurrency(selectedInvoice.amount)}</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
                
                {selectedInvoice.status === "paid" && (
                  <div className="bg-green-50 p-4 rounded-md">
                    <p className="text-green-800 font-medium">Payment Information</p>
                    <p className="text-sm text-green-700">
                      Paid on {format(selectedInvoice.paymentDate, "MMMM d, yyyy")} via {selectedInvoice.paymentMethod === "card" ? "Credit Card" : "Bank Transfer"}
                    </p>
                  </div>
                )}
                
                {selectedInvoice.status === "pending" && (
                  <div className="bg-yellow-50 p-4 rounded-md">
                    <p className="text-yellow-800 font-medium">Payment Methods</p>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" className="w-full">
                          <CreditCard className="mr-2 h-4 w-4" />
                          Pay with Card
                        </Button>
                      </div>
                      <p className="text-sm text-yellow-700">
                        Or make a bank transfer to:<br />
                        Account Name: I am Coming<br />
                        BSB: 123-456<br />
                        Account Number: ********<br />
                        Reference: {selectedInvoice.id}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsViewInvoiceOpen(false)}>Close</Button>
              <Button>
                <Download className="mr-2 h-4 w-4" />
                Download PDF
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </PartnerLayout>
  )
}
