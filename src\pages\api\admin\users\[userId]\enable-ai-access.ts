/**
 * API endpoint to enable AI access for testing purposes
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { Database } from '@/lib/database';
import { authConfig } from '@/auth';
import { log } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check authentication
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { userId } = req.query;

    if (!userId || typeof userId !== 'string') {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // For testing purposes, allow users to enable AI access for themselves
    // In production, this should be restricted to admins only
    if (userId !== session.user.id) {
      return res.status(403).json({ error: 'Can only enable AI access for yourself in test mode' });
    }

    const db = Database.getInstance();

    // Get user data
    const user = await db.readData('users', userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Enable AI access
    await db.updateData('users', userId, {
      hasAiAccess: true,
      aiAccessEnabledAt: new Date().toISOString(),
    });

    log('AI access enabled for user', { userId, enabledBy: session.user.id });

    return res.status(200).json({
      success: true,
      message: 'AI access enabled successfully',
      user: {
        id: userId,
        hasAiAccess: true,
      },
    });

  } catch (error) {
    console.error('Error enabling AI access:', error);
    log('Error enabling AI access', { error, userId: req.query.userId });
    
    return res.status(500).json({ 
      error: 'Internal server error' 
    });
  }
}
