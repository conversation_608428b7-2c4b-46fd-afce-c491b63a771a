'use client'

import { useState } from "react"
import { PartnerLayout } from "@/components/layouts/PartnerLayout"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/router"
import { Calendar, Mail, Phone, Plus, Search, User, Users } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useSession } from "next-auth/react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"

// Mock data for customers
interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  events: number;
  lastEvent: Date | null;
}

const mockCustomers: Customer[] = [
  {
    id: "cust-1",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+61 412 345 678",
    events: 3,
    lastEvent: new Date(2023, 11, 15)
  },
  {
    id: "cust-2",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "+61 423 456 789",
    events: 1,
    lastEvent: new Date(2023, 10, 20)
  },
  {
    id: "cust-3",
    name: "Michael Brown",
    email: "<EMAIL>",
    phone: "+61 434 567 890",
    events: 2,
    lastEvent: new Date(2023, 9, 5)
  }
];

export default function CustomerManagement() {
  const router = useRouter()
  const { data: session } = useSession()
  const [customers, setCustomers] = useState<Customer[]>(mockCustomers)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddCustomerOpen, setIsAddCustomerOpen] = useState(false)
  const [isViewCustomerOpen, setIsViewCustomerOpen] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [newCustomer, setNewCustomer] = useState({
    name: "",
    email: "",
    phone: ""
  })
  
  // Filter customers based on search term
  const filteredCustomers = customers.filter(customer => 
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phone.includes(searchTerm)
  )
  
  const handleAddCustomer = () => {
    const customer: Customer = {
      id: `cust-${Date.now()}`,
      name: newCustomer.name,
      email: newCustomer.email,
      phone: newCustomer.phone,
      events: 0,
      lastEvent: null
    };
    
    setCustomers([...customers, customer]);
    setNewCustomer({ name: "", email: "", phone: "" });
    setIsAddCustomerOpen(false);
  };
  
  const handleViewCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsViewCustomerOpen(true);
  };
  
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
  };
  
  const formatDate = (date: Date | null) => {
    if (!date) return "N/A";
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  return (
    <PartnerLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Customer Management</h1>
            <p className="text-gray-500">Manage your customers and their events</p>
          </div>
          <Dialog open={isAddCustomerOpen} onOpenChange={setIsAddCustomerOpen}>
            <DialogTrigger asChild>
              <Button variant="primary-button">
                <Plus className="mr-2 h-4 w-4" />
                Add Customer
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Customer</DialogTitle>
                <DialogDescription>
                  Enter the details for your new customer.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="customer-name">Full Name</Label>
                  <Input 
                    id="customer-name" 
                    placeholder="Enter customer name" 
                    value={newCustomer.name}
                    onChange={(e) => setNewCustomer({...newCustomer, name: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="customer-email">Email</Label>
                  <Input 
                    id="customer-email" 
                    type="email"
                    placeholder="Enter customer email" 
                    value={newCustomer.email}
                    onChange={(e) => setNewCustomer({...newCustomer, email: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="customer-phone">Phone Number</Label>
                  <Input 
                    id="customer-phone" 
                    placeholder="Enter customer phone" 
                    value={newCustomer.phone}
                    onChange={(e) => setNewCustomer({...newCustomer, phone: e.target.value})}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddCustomerOpen(false)}>Cancel</Button>
                <Button 
                  variant="default" 
                  onClick={handleAddCustomer}
                  disabled={!newCustomer.name || !newCustomer.email}
                >
                  Add Customer
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
        
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input 
            placeholder="Search customers by name, email, or phone..." 
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        {/* Customers Table */}
        <Card>
          <CardHeader>
            <CardTitle>Customers</CardTitle>
            <CardDescription>
              View and manage your customer list
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredCustomers.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">
                  {searchTerm ? "No customers match your search criteria." : "No customers have been added yet."}
                </p>
                {!searchTerm && (
                  <Button 
                    variant="outline" 
                    className="mt-4"
                    onClick={() => setIsAddCustomerOpen(true)}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Your First Customer
                  </Button>
                )}
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Events</TableHead>
                    <TableHead>Last Event</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCustomers.map((customer) => (
                    <TableRow key={customer.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarFallback>{getInitials(customer.name)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{customer.name}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center text-sm">
                            <Mail className="h-3.5 w-3.5 mr-1 text-gray-500" />
                            {customer.email}
                          </div>
                          <div className="flex items-center text-sm">
                            <Phone className="h-3.5 w-3.5 mr-1 text-gray-500" />
                            {customer.phone}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{customer.events}</TableCell>
                      <TableCell>{formatDate(customer.lastEvent)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleViewCustomer(customer)}
                          >
                            View
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => router.push('/partner/events/new')}
                          >
                            Create Event
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
        
        {/* Customer Details Dialog */}
        <Dialog open={isViewCustomerOpen} onOpenChange={setIsViewCustomerOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Customer Details</DialogTitle>
            </DialogHeader>
            
            {selectedCustomer && (
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <Avatar className="h-16 w-16">
                    <AvatarFallback className="text-lg">{getInitials(selectedCustomer.name)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="text-xl font-semibold">{selectedCustomer.name}</h3>
                    <div className="flex items-center text-gray-500 mt-1">
                      <Mail className="h-4 w-4 mr-1" />
                      {selectedCustomer.email}
                    </div>
                    <div className="flex items-center text-gray-500">
                      <Phone className="h-4 w-4 mr-1" />
                      {selectedCustomer.phone}
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="p-4 flex flex-col items-center text-center">
                      <Calendar className="h-8 w-8 text-primary mb-2" />
                      <p className="text-2xl font-bold">{selectedCustomer.events}</p>
                      <p className="text-sm text-gray-500">Total Events</p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-4 flex flex-col items-center text-center">
                      <Calendar className="h-8 w-8 text-primary mb-2" />
                      <p className="text-sm font-medium">{formatDate(selectedCustomer.lastEvent)}</p>
                      <p className="text-sm text-gray-500">Last Event</p>
                    </CardContent>
                  </Card>
                </div>
                
                {selectedCustomer.events > 0 ? (
                  <div>
                    <h4 className="font-medium mb-2">Recent Events</h4>
                    <Card>
                      <CardContent className="p-4">
                        <p className="text-sm text-gray-500">Event history will be displayed here.</p>
                      </CardContent>
                    </Card>
                  </div>
                ) : (
                  <Card>
                    <CardContent className="p-4 text-center">
                      <p className="text-gray-500 mb-4">This customer hasn&apos;t had any events yet.</p>
                      <Button 
                        variant="outline"
                        onClick={() => {
                          setIsViewCustomerOpen(false);
                          router.push('/partner/events/new');
                        }}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Create First Event
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsViewCustomerOpen(false)}>Close</Button>
              <Button 
                variant="default"
                onClick={() => {
                  setIsViewCustomerOpen(false);
                  router.push('/partner/events/new');
                }}
              >
                Create Event
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </PartnerLayout>
  )
}
