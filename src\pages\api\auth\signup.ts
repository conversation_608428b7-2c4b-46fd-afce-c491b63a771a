import { NextApiRequest, NextApiResponse } from 'next';
import { Database } from '@/lib/database';
import { validatePassword } from '@/lib/auth/password';
import { z } from 'zod';
import { debugLog } from '@/lib/logger';

// Define validation schema for signup request
const signupSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  name: z.string().optional(),
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Validate request body
    const validationResult = signupSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: validationResult.error.errors 
      });
    }

    const { email, password, name } = validationResult.data;

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.success) {
      return res.status(400).json({ error: passwordValidation.error });
    }

    // Get database instance
    const db = Database.getInstance();

    // Check if user already exists
    const existingUser = await db.getUserByEmail(email);
    if (existingUser) {
      return res.status(409).json({ error: 'User with this email already exists' });
    }

    // Create user with password
    const { id } = await db.createUserWithPassword(email, password, name);

    debugLog('User created successfully:', { id, email });

    // Return success response
    return res.status(201).json({ 
      success: true, 
      message: 'User created successfully',
      userId: id
    });
  } catch (error) {
    console.error('Error in signup handler:', error);
    return res.status(500).json({ error: 'Failed to create user' });
  }
}
