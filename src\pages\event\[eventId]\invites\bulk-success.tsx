"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/router"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Check, Mail, MessageSquare, Loader2 } from "lucide-react"
import { useEvent } from "@/hooks/useEvent"
import { useToast } from "@/components/ui/use-toast"
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout"
import { EventInvite } from "@/types"

export default function BulkInviteSuccessPage() {
  const router = useRouter()
  const { eventId } = router.query
  const { event } = useEvent(eventId as string)
  const [sending, setSending] = useState(false)
  const [emailsSent, setEmailsSent] = useState(0)
  const { toast } = useToast()

  // Get imported invites from router state
  const [importedInvites, setImportedInvites] = useState<EventInvite[]>([])
  const [importType, setImportType] = useState<'bulk' | 'groups'>('bulk')
  
  useEffect(() => {
    // Only proceed if eventId is available
    if (!eventId) return;
    
    console.log('Looking for bulk import data for eventId:', eventId);
    
    // Get the data from sessionStorage (set by the import page)
    const storageKey = `bulkImport_${eventId}`;
    const importData = sessionStorage.getItem(storageKey);
    
    console.log('Found sessionStorage data:', importData);
    
    if (importData) {
      try {
        const parsedData = JSON.parse(importData);
        console.log('Parsed import data:', parsedData);
        
        if (parsedData.importedInvites && parsedData.importedInvites.length > 0) {
          setImportedInvites(parsedData.importedInvites);
          setImportType(parsedData.importType || 'bulk');
          // Clear the data from sessionStorage after using it
          sessionStorage.removeItem(storageKey);
          console.log('Successfully loaded import data:', parsedData.importedInvites.length, 'invites');
        } else {
          console.log('No imported invites found in data, redirecting...');
          router.push(`/event/${eventId}/invites`);
        }
      } catch (error) {
        console.error('Error parsing import data:', error);
        router.push(`/event/${eventId}/invites`);
      }
    } else {
      console.log('No import data found in sessionStorage, redirecting...');
      // If no data, redirect back to invites page
      router.push(`/event/${eventId}/invites`);
    }
  }, [eventId, router]);

  // Filter invites that have email addresses
  const invitesWithEmail = importedInvites.filter(invite => 
    invite.email && invite.email.trim() !== ''
  )

  const handleSendEmails = async () => {
    if (invitesWithEmail.length === 0) {
      toast({
        title: "No email addresses",
        description: "None of the imported contacts have email addresses.",
        variant: "destructive"
      })
      return
    }

    setSending(true)
    setEmailsSent(0)

    try {
      let successCount = 0
      
      // Send emails in batches to avoid overwhelming the server
      const batchSize = 5
      for (let i = 0; i < invitesWithEmail.length; i += batchSize) {
        const batch = invitesWithEmail.slice(i, i + batchSize)
        
        const batchPromises = batch.map(async (invite) => {
          try {
            const response = await fetch(`/api/event/${eventId}/invites/${invite.ID}/send-email`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
            })

            if (response.ok) {
              successCount++
              setEmailsSent(successCount)
              return true
            } else {
              console.error(`Failed to send email to ${invite.email}`)
              return false
            }
          } catch (error) {
            console.error(`Error sending email to ${invite.email}:`, error)
            return false
          }
        })

        await Promise.all(batchPromises)
        
        // Small delay between batches
        if (i + batchSize < invitesWithEmail.length) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      toast({
        title: "Invitations sent!",
        description: `Successfully sent ${successCount} invitation email${successCount !== 1 ? 's' : ''}.`,
      })

    } catch (error) {
      console.error('Error sending invitation emails:', error)
      toast({
        title: "Error",
        description: "Failed to send some invitation emails. Please try again.",
        variant: "destructive"
      })
    } finally {
      setSending(false)
    }
  }

  // Handle done button
  const handleDone = () => {
    router.push(`/event/${eventId}/invites`)
  }

  const importTypeText = importType === 'bulk' ? 'CSV upload' : 'saved contact groups'

  if (importedInvites.length === 0) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </ProtectedLayout>
    )
  }

  return (
    <ProtectedLayout>
      <div className="flex items-center justify-center min-h-[calc(100vh-64px)] bg-gray-50 p-4">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="pt-6 pb-4 px-6">
            <div className="flex flex-col items-center text-center">
              {/* Success icon */}
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <Check className="h-8 w-8 text-green-500" />
              </div>

              {/* Success message */}
              <h2 className="text-2xl font-semibold mb-2">Contacts Imported Successfully</h2>
              <p className="text-gray-500 mb-6">
                {importedInvites.length} contact{importedInvites.length !== 1 ? 's' : ''} imported from {importTypeText}<br />
                Send invitations to your guests
              </p>

              {/* Availability message */}
              {invitesWithEmail.length !== importedInvites.length && (
                <div className="w-full py-1 px-4 bg-yellow-50 border border-amber-300 text-center rounded-md text-sm mb-3">
                  {importedInvites.length - invitesWithEmail.length} contact{importedInvites.length - invitesWithEmail.length !== 1 ? 's' : ''} without email will be skipped
                </div>
              )}

              {/* Send invitation buttons */}
              <Button
                variant="outline"
                onClick={handleSendEmails}
                className="flex items-center justify-center w-full mb-3"
                disabled={invitesWithEmail.length === 0 || sending}
              >
                {sending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Sending... ({emailsSent}/{invitesWithEmail.length})
                  </>
                ) : (
                  <>
                    <Mail className="h-4 w-4 mr-2" />
                    <span className="hidden sm:inline">Send invitation over email</span>
                    <span className="sm:hidden">Email Invite</span>
                  </>
                )}
              </Button>

              {/* No email availability message */}
              {invitesWithEmail.length === 0 && (
                <div className="w-full py-1 px-4 bg-yellow-50 border border-yellow-100 text-center rounded-md text-sm mb-3">
                  Email addresses are not available
                </div>
              )}

              <Button
                variant="outline"
                className="flex items-center justify-center w-full mb-6"
                disabled={true}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Send invitation as a text</span>
                <span className="sm:hidden">Text Invite</span>
                <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">Coming Soon</span>
              </Button>

              {/* Done button */}
              <Button
                variant='primary-button'
                onClick={handleDone}
                className="w-full"
                disabled={sending}
              >
                Done
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </ProtectedLayout>
  )
}
