"use client"

import { EventInviteListItem } from "@/types";
import { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Search } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface InviteSelectorProps {
  invites: EventInviteListItem[];
  selectedInvites?: string[];
  onChange: (selectedIds: string[]) => void;
}

export function InviteSelector({ invites, selectedInvites = [], onChange }: InviteSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [topPosition, setTopPosition] = useState(0);
  const searchRef = useRef<HTMLDivElement>(null);
  const infoRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updatePosition = () => {
      if (searchRef.current && infoRef.current) {
        const searchHeight = searchRef.current.clientHeight;
        const infoHeight = infoRef.current.clientHeight;
        const gap = 16; // gap-4
        setTopPosition(searchHeight + infoHeight + (gap * 2));
      }
    };

    updatePosition();
    window.addEventListener('resize', updatePosition);
    return () => window.removeEventListener('resize', updatePosition);
  }, []);


  // Filter invites based on search query
  const filteredInvites = invites?.filter(invite =>
    invite.name.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];  // Get all unique groups including null/undefined (which will be shown as "Ungrouped")
  const groups = [...new Set(filteredInvites.map(invite => invite.group || null))]
    .sort((a, b) => {
      // Always show null/ungrouped first
      if (a === null) return -1;
      if (b === null) return 1;
      // Sort other groups alphabetically
      return a.localeCompare(b);
    });
  const totalSelected = selectedInvites.length;

  // Handler for invite selection
  const toggleInvite = (inviteId: string) => {
    const newSelection = selectedInvites.includes(inviteId)
      ? selectedInvites.filter(id => id !== inviteId)
      : [...selectedInvites, inviteId];
    onChange(newSelection);
  };

  // Handler for group selection
  const toggleGroup = (group: string | null) => {
    const groupInvites = filteredInvites.filter(invite => (invite.group || null) === group);
    const groupInviteIds = groupInvites.map(invite => invite.ID);
    const allGroupSelected = groupInviteIds.every(id => selectedInvites.includes(id));

    let newSelection: string[];
    if (allGroupSelected) {
      // Deselect all invites in this group
      newSelection = selectedInvites.filter(id => !groupInviteIds.includes(id));
    } else {
      // Select all invites in this group
      newSelection = [...new Set([...selectedInvites, ...groupInviteIds])];
    }
    onChange(newSelection);
  };

  // Check if a group is fully selected
  const isGroupSelected = (group: string | null) => {
    const groupInvites = filteredInvites.filter(invite => (invite.group || null) === group);
    const groupInviteIds = groupInvites.map(invite => invite.ID);
    return groupInviteIds.length > 0 && groupInviteIds.every(id => selectedInvites.includes(id));
  };

  // Check if a group is partially selected
  const isGroupPartiallySelected = (group: string | null) => {
    const groupInvites = filteredInvites.filter(invite => (invite.group || null) === group);
    const groupInviteIds = groupInvites.map(invite => invite.ID);
    const selectedInGroup = groupInviteIds.filter(id => selectedInvites.includes(id));
    return selectedInGroup.length > 0 && selectedInGroup.length < groupInviteIds.length;
  };

  if (!invites?.length) {
    return (
      <div className="space-y-4">
        <div className="flex items-center">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search invites..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
            disabled
          />
        </div>
        <div className="text-sm text-muted-foreground text-center py-8">
          No invites available
        </div>
      </div>
    );
  }

  return (
    <div className="h-full relative">
      <div className="text-md font-medium mb-1 -mt-2">Select Invites</div>
      <div ref={searchRef} className="relative flex items-center">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
        <Input
          placeholder="Search invites..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      <div ref={infoRef} className="text-sm text-muted-foreground mt-1 font-normal">
        Showing {filteredInvites.length} invites in {groups.length} groups. {totalSelected} invites selected
      </div>

      <div
        className="absolute w-full overflow-y-auto bg-white pt-4"
        style={{
          top: topPosition + 94,
          bottom: 0,
        }}
      >        <div className="border border-gray-200 rounded-lg bg-white pb-6">
          <Accordion type="multiple" className="w-full" defaultValue={groups.map(group => group ?? 'ungrouped')}>
            {groups.map((group, index) => {
              const groupKey = group ?? 'ungrouped';
              const groupInvites = filteredInvites.filter(invite => (invite.group || null) === group);
              const isLastGroup = index === groups.length - 1;

              return (
                <AccordionItem key={groupKey} value={groupKey} className={cn("border-b-0", !isLastGroup && "border-b border-gray-200")}>
                  <AccordionTrigger className="hover:no-underline py-3 px-4 data-[state=open]:border-b data-[state=open]:border-gray-200 rounded-none">
                    <div className="flex items-center space-x-3 w-full">
                      <Checkbox
                        checked={isGroupSelected(group)}
                        ref={(el) => {
                          if (el && isGroupPartiallySelected(group)) {
                            const checkbox = el.querySelector('input[type="checkbox"]') as HTMLInputElement;
                            if (checkbox) {
                              checkbox.indeterminate = true;
                            }
                          }
                        }}
                        onCheckedChange={() => toggleGroup(group)}
                        onClick={(e) => e.stopPropagation()}
                        className="shrink-0"
                      />
                      <span className="text-sm font-semibold text-left flex-1">
                        {group || 'Ungrouped'}
                      </span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="pb-2">
                    <div className="space-y-0 ml-4">
                      {groupInvites.map(invite => (
                        <div key={invite.ID} className="flex items-center space-x-3 py-2">
                          <Checkbox
                            checked={selectedInvites.includes(invite.ID)}
                            onCheckedChange={() => toggleInvite(invite.ID)}
                            id={`invite-${invite.ID}`}
                            className="shrink-0 mt-1"
                          />                          <span
                            className="text-sm cursor-pointer flex-1 mt-1"
                            onClick={() => toggleInvite(invite.ID)}
                          >
                            {invite.name}
                          </span>
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              );
            })}
          </Accordion>
        </div>
      </div>
    </div >
  );
}
