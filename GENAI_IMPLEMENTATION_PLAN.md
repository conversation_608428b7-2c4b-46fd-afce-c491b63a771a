# GenAI Service Implementation Plan

## Overview
Implement a comprehensive GenAI service using Gemini AI for generating digital invites, printable invites, and event descriptions with rate limiting and usage counters.

## Project Context
- **Framework**: Next.js with TypeScript
- **AI Provider**: Google Gemini AI (already configured with API key)
- **Image Processing**: Sharp (already installed)
- **Database**: Firebase Firestore
- **Authentication**: NextAuth.js

## Digital Invite Dimensions Analysis
Based on existing code analysis:
- **Digital Invite (Mobile)**: 400px width (responsive), 300px height for background image
- **Digital Invite (Desktop)**: 800px width, 600px height
- **Printable Invite**: Various paper sizes (A4: 2480x3508, A5: 1748x2480, A6: 1240x1748, etc.)

## Implementation Phases

### Phase 1: Core GenAI Service Infrastructure (IAC-108)
**Files to create/modify:**
- `src/lib/genai/index.ts` - Main GenAI service class
- `src/lib/genai/types.ts` - TypeScript interfaces and types
- `src/lib/genai/config.ts` - Configuration and constants
- `src/lib/genai/rate-limiter.ts` - Rate limiting service (already exists, needs review)
- `src/lib/genai/prompts.ts` - Prompt templates

**Key Features:**
- Gemini AI client initialization
- Error handling and logging
- Configuration management
- Base service structure

### Phase 2: Prompt Templates (IAC-106)
**Files to create:**
- `src/lib/genai/templates/digital-invite.ts` - Digital invite generation prompts
- `src/lib/genai/templates/printable-invite.ts` - Printable invite generation prompts  
- `src/lib/genai/templates/event-description.ts` - Event description generation prompts

**Templates to generate:**
1. **Digital Invite Image**: Mobile-optimized (400x300px background area)
2. **Printable Invite Image**: Print-optimized (various paper sizes)
3. **Event Description**: Text-based event descriptions

### Phase 3: Digital Invite Generation (IAC-103)
**Files to create:**
- `src/lib/genai/digital-invite.ts` - Digital invite generation logic
- `src/pages/api/genai/digital-invite.ts` - API endpoint

**Features:**
- Generate digital invite images using Gemini AI
- Support for event context and customization
- Image optimization for web display
- Integration with existing digital invite preview

### Phase 4: Printable Invite Generation (IAC-104)
**Files to create:**
- `src/lib/genai/printable-invite.ts` - Printable invite generation logic
- `src/pages/api/genai/printable-invite.ts` - API endpoint

**Features:**
- Generate printable invite images
- Support for various paper sizes and orientations
- High-resolution output for printing
- Integration with existing print workflow

### Phase 5: Event Description Generation (IAC-105)
**Files to create:**
- `src/lib/genai/event-description.ts` - Event description generation logic
- `src/pages/api/genai/event-description.ts` - API endpoint

**Features:**
- Generate compelling event descriptions
- Support for different tones and styles
- Integration with event creation/editing forms

### Phase 6: Usage Counter and Rate Limiting (IAC-107)
**Files to modify:**
- `src/types/index.ts` - Add usage counter fields to Event interface
- `src/lib/database.ts` - Add usage counter methods
- `src/lib/genai/rate-limiter.ts` - Enhance existing rate limiter

**Features:**
- Add usage counters to Event model
- Implement per-event and per-user rate limiting
- Track AI generation usage
- Prevent abuse and control costs

## Technical Specifications

### Image Generation Approach
Since Gemini AI doesn't directly generate images, we'll use a hybrid approach:
1. **Text-to-Prompt**: Use Gemini to generate detailed image descriptions
2. **SVG Generation**: Convert descriptions to SVG code using Gemini
3. **Image Rendering**: Use Sharp to render SVG to various image formats

### Rate Limiting Strategy
- **Free Users**: 5 generations per day
- **Paid Users**: 50 generations per day  
- **Admin Users**: Unlimited
- **Per-Event Limit**: 10 generations per event to prevent abuse

### Database Schema Changes
```typescript
// Add to Event interface
interface Event {
  // ... existing fields
  aiUsageCounters?: {
    digitalInviteGenerations: number;
    printableInviteGenerations: number;
    descriptionGenerations: number;
    totalGenerations: number;
    lastGenerationDate: Date;
  };
}
```

### API Endpoints Structure
```
/api/genai/digital-invite
/api/genai/printable-invite  
/api/genai/event-description
/api/genai/usage-stats
```

## Security Considerations
- Rate limiting per user and per event
- Input validation and sanitization
- API key protection
- User authentication required
- Admin-only access controls for usage management

## Testing Strategy
- Unit tests for each service component
- Integration tests for API endpoints
- Rate limiting tests
- Image generation quality tests
- Performance and load testing

## Deployment Considerations
- Environment variable configuration
- API key security
- Rate limiting configuration
- Monitoring and logging
- Error tracking and alerting

## Success Metrics
- Successful image generation rate
- User adoption of AI features
- Rate limiting effectiveness
- API response times
- Cost per generation tracking

## Implementation Status
- [x] Phase 1: Core GenAI Service Infrastructure (IAC-108) ✅
- [x] Phase 2: Prompt Templates (IAC-106) ✅
- [x] Phase 3: Digital Invite Generation (IAC-103) ✅
- [x] Phase 4: Printable Invite Generation (IAC-104) ✅
- [x] Phase 5: Event Description Generation (IAC-105) ✅
- [x] Phase 6: Usage Counter and Rate Limiting (IAC-107) ✅

## Test Page
A comprehensive test page has been created at `/test/genai` that allows you to:
- Test all three generation types (digital invites, printable invites, event descriptions)
- Configure generation options (themes, colors, paper sizes, etc.)
- View usage statistics and rate limiting status
- See real-time generation results and error handling
- Test with different event data and parameters
- Enable AI access for testing purposes
- Monitor rate limits and usage quotas

### How to Test
1. **Access**: Navigate to `/test/genai` or use Admin Panel → GenAI Test
2. **Enable AI Access**: Click "Enable for Testing" if AI access is disabled
3. **Configure Event Data**: Fill in the event information form
4. **Test Generation**: Try all three tabs (Digital, Printable, Description)
5. **Monitor Usage**: Check usage statistics and rate limits

See `GENAI_TESTING_GUIDE.md` for detailed testing instructions.

## Next Steps
1. Start with Phase 1 (Core Infrastructure)
2. Implement and test each phase incrementally
3. Gather user feedback and iterate
4. Monitor usage and costs
5. Optimize performance and quality

## Dependencies
- `@google/generative-ai` (already installed)
- `sharp` (already installed)
- Existing rate limiting infrastructure
- Firebase Firestore for usage tracking
- NextAuth.js for user authentication
