# GenAI Service Testing Guide

## Overview
This guide explains how to test the newly implemented GenAI service that generates **actual images** for digital invites and printable invites, plus event descriptions using Google Gemini AI's image generation model `gemini-2.0-flash-preview-image-generation`.

## Prerequisites

### 1. Environment Setup
Ensure you have the Gemini API key configured in your `.env.local` file:
```
GEMINI_API_KEY=your_gemini_api_key_here
```

### 2. User Authentication
- Sign in to the application
- You need to be authenticated to access the GenAI features

### 3. AI Access Permission
- AI access is disabled by default for security
- Use the test page to enable AI access for your account

## Testing Steps

### Step 1: Access the Test Page
1. Sign in to the application
2. Navigate to the admin panel (if you're an admin) or go directly to `/test/genai`
3. The test page is also accessible via the admin sidebar: **Admin Panel → GenAI Test**

### Step 2: Enable AI Access
1. On the test page, check your **Usage Statistics** section
2. If "AI Access" shows "Disabled", click the **"Enable for Testing"** button
3. This will enable AI features for your account

### Step 3: Configure Event Data
1. Fill in the **Event Information** form with test data:
   - Event Name: e.g., "Birthday Party"
   - Host: e.g., "<PERSON>"
   - Date: Select a future date
   - Location: e.g., "123 Main Street, Anytown"
   - Start/End Time: Set appropriate times
   - Message: Add a custom message

### Step 4: Test Digital Invite Generation
1. Go to the **Digital Invite** tab
2. Configure options:
   - **Theme**: Choose from Modern, Classic, Elegant, Fun, or Minimal
   - **Color Scheme**: Choose from Warm, Cool, Neutral, or Vibrant
3. Click **"Generate Digital Invite"**
4. Check the result:
   - ✅ Success: You'll see the generated **image** displayed with dimensions and usage statistics
   - ❌ Error: Check the error message and your rate limits

### Step 5: Test Printable Invite Generation
1. Go to the **Printable Invite** tab
2. Configure options:
   - **Paper Size**: A4, A5, A6, or photo sizes
   - **Orientation**: Portrait or Landscape
   - **Theme**: Elegant, Classic, Modern, or Minimal
3. Click **"Generate Printable"**
4. Review the high-resolution **image** optimized for printing with paper size and orientation details

### Step 6: Test Event Description Generation
1. Go to the **Event Description** tab
2. Configure options:
   - **Tone**: Formal, Casual, Friendly, Professional, or Exciting
   - **Length**: Short, Medium, or Long
   - **Include Details**: Check/uncheck as needed
3. Click **"Generate Description"**
4. Review the generated:
   - Title
   - Full description
   - Short description
   - Keywords

### Step 7: Monitor Usage and Rate Limits
1. Check the **Usage Statistics** section regularly
2. Monitor:
   - Daily usage count
   - Remaining quota
   - User type (free/paid)
   - Reset time

## Rate Limits

### Free Users
- **Daily Limit**: 5 generations per day
- **Per-Event Limit**: 3 generations per event

### Paid Users
- **Daily Limit**: 50 generations per day
- **Per-Event Limit**: 10 generations per event

### Admin Users
- **Daily Limit**: 1000 generations per day
- **Per-Event Limit**: 100 generations per event

## API Endpoints

The following API endpoints are available for integration:

### Digital Invite Generation
```
POST /api/genai/digital-invite
```

### Printable Invite Generation
```
POST /api/genai/printable-invite
```

### Event Description Generation
```
POST /api/genai/event-description
```

### Usage Statistics
```
GET /api/genai/usage-stats
```

## Troubleshooting

### Common Issues

#### 1. "AI features are not enabled for your account"
- **Solution**: Use the "Enable for Testing" button on the test page
- **Cause**: AI access is disabled by default for security

#### 2. "Daily limit exceeded"
- **Solution**: Wait for the daily reset (midnight) or upgrade user type
- **Cause**: You've reached your daily generation limit

#### 3. "Generation failed" or timeout errors
- **Solution**: Check your Gemini API key and internet connection
- **Cause**: API key issues or network problems

#### 4. "Event not found" error
- **Solution**: Ensure you're using a valid event ID
- **Cause**: The test page creates temporary event IDs

#### 5. Empty or malformed SVG output
- **Solution**: Try different themes or regenerate
- **Cause**: Gemini AI occasionally produces unexpected output

### Debug Information

Each generation result includes:
- **Generation Time**: How long the AI took to respond
- **Tokens Used**: API usage metrics
- **Remaining Quota**: Your remaining daily limit

## Production Considerations

### Security
- AI access should be controlled via admin panel in production
- Rate limiting prevents abuse and controls costs
- Input validation prevents malicious prompts

### Performance
- Results are cached for 1 hour to improve performance
- Generation typically takes 5-15 seconds
- Timeout is set to 30 seconds

### Cost Management
- Monitor usage through the statistics endpoint
- Rate limits control API costs
- Consider implementing usage alerts

## Next Steps

1. **Test all three generation types** with different options
2. **Verify rate limiting** by exceeding daily limits
3. **Test error handling** with invalid inputs
4. **Check caching** by generating the same content twice
5. **Monitor performance** and generation times

## Support

If you encounter issues:
1. Check the browser console for detailed error messages
2. Verify your Gemini API key is valid
3. Ensure you have AI access enabled
4. Check your rate limit status
5. Review the server logs for backend errors

The GenAI service is now ready for testing and integration into your event management workflow!
