'use client'

import { useState, useEffect } from "react"
import { PartnerLayout } from "@/components/layouts/PartnerLayout"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useSession } from "next-auth/react"
import { useRouter } from "next/router"
import { Building2, Calendar, Users, CreditCard, Clock, Plus, QrCode } from "lucide-react"
import { useEvents } from "@/hooks/useEvents"

export default function PartnerDashboard() {
  const { data: session } = useSession()
  const router = useRouter()
  const { events, loading, error } = useEvents()

  // Filter events created by this partner
  const partnerEvents = events.filter(event => event.organizationId === session?.user?.organization?.id)

  // Count statistics
  const totalEvents = partnerEvents.length
  const upcomingEvents = partnerEvents.filter(event => new Date(event.date as Date) > new Date()).length
  const pastEvents = partnerEvents.filter(event => new Date(event.date as Date) <= new Date()).length

  return (
    <PartnerLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Partner Dashboard</h1>
            <p className="text-gray-500">Manage your venues, events, and customers</p>
          </div>
          <Button
            variant="primary-button"
            onClick={() => router.push('/partner/events/new')}
          >
            <Plus className="mr-2 h-4 w-4" />
            Create Event
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Total Events</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold">{totalEvents}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Upcoming Events</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold">{upcomingEvents}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Past Events</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold">{pastEvents}</p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-xl font-bold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push('/partner/events/new')}>
              <CardContent className="p-6 flex flex-col items-center text-center">
                <Calendar className="h-10 w-10 text-primary mb-4" />
                <CardTitle className="text-lg mb-2">Create Event</CardTitle>
                <CardDescription>Create a new event for a customer</CardDescription>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push('/partner/venues')}>
              <CardContent className="p-6 flex flex-col items-center text-center">
                <Building2 className="h-10 w-10 text-primary mb-4" />
                <CardTitle className="text-lg mb-2">Manage Venues</CardTitle>
                <CardDescription>Add or edit your venues</CardDescription>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push('/partner/time-slots')}>
              <CardContent className="p-6 flex flex-col items-center text-center">
                <Clock className="h-10 w-10 text-primary mb-4" />
                <CardTitle className="text-lg mb-2">Time Slots</CardTitle>
                <CardDescription>Manage available time slots</CardDescription>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push('/partner/invoices')}>
              <CardContent className="p-6 flex flex-col items-center text-center">
                <CreditCard className="h-10 w-10 text-primary mb-4" />
                <CardTitle className="text-lg mb-2">View Invoices</CardTitle>
                <CardDescription>Check your billing status</CardDescription>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push('/partner/entry')}>
              <CardContent className="p-6 flex flex-col items-center text-center">
                <QrCode className="h-10 w-10 text-primary mb-4" />
                <CardTitle className="text-lg mb-2">Entry Control</CardTitle>
                <CardDescription>Scan QR codes for guest entry</CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Recent Events */}
        <div>
          <h2 className="text-xl font-bold mb-4">Recent Events</h2>
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : partnerEvents.length === 0 ? (
            <Card className="text-center p-6">
              <CardDescription>
                You haven&apos;t created any events yet. Create your first event to get started.
              </CardDescription>
              <Button variant="primary-button" onClick={() => router.push('/partner/events/new')} className="mt-4">
                <Plus className="mr-2 h-4 w-4" />
                Create Event
              </Button>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {partnerEvents.slice(0, 3).map((event) => (
                <Card key={event.id} className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push(`/event/${event.id}`)}>
                  <CardHeader>
                    <CardTitle>{event.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-500">{new Date(event.date as Date).toLocaleDateString()}</p>
                    <p className="text-sm text-gray-500">{event.location}</p>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" className="w-full" onClick={(e) => {
                      e.stopPropagation();
                      router.push(`/event/${event.id}`);
                    }}>
                      View Details
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}

          {partnerEvents.length > 0 && (
            <div className="mt-4 text-center">
              <Button variant="outline" onClick={() => router.push('/partner/events')}>
                View All Events
              </Button>
            </div>
          )}
        </div>
      </div>
    </PartnerLayout>
  )
}
