'use client'

import { <PERSON><PERSON> } from "@/components/Header"
import { <PERSON><PERSON> } from "@/components/Footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Check, ChevronLeft, ChevronRight } from "lucide-react"
import { useRouter } from "next/router"
import { useEffect, useState, useRef } from "react"
import { plans } from "@/lib/plans"
import RemoteConfig from "@/lib/remoteConfig"
import { Badge } from "@/components/ui/badge"
import { Ribbon } from "@/components/ui/ribbon"
import { useSession } from "next-auth/react"

export default function PricingPage() {
  const router = useRouter()
  const { data: session } = useSession()
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)
  // Beta mode is always enabled for this UI
  const [_isBetaMode, _setIsBetaMode] = useState(true)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [showLeftScroll, setShowLeftScroll] = useState(false)
  const [showRightScroll, setShowRightScroll] = useState(true) // Initialize as true to show it on first load

  // Beta mode is always enabled for this UI
  useEffect(() => {
    // No need to check remote config as we're always showing beta mode
  }, []);

  // Check if scroll buttons should be visible
  useEffect(() => {
    const checkScrollButtons = () => {
      const container = scrollContainerRef.current;
      if (container) {
        // Show left button if scrolled to the right
        setShowLeftScroll(container.scrollLeft > 10);
        
        // Show right button if there's more content to scroll to
        const hasMoreToScroll = container.scrollWidth > container.clientWidth + 20 &&
          container.scrollLeft < (container.scrollWidth - container.clientWidth - 10);
        
        setShowRightScroll(hasMoreToScroll);
        
        // For debugging
        console.log("Scroll check:", {
          scrollWidth: container.scrollWidth,
          clientWidth: container.clientWidth,
          scrollLeft: container.scrollLeft,
          hasOverflow: container.scrollWidth > container.clientWidth,
          hasMoreToScroll
        });
      }
    };

    // Force a check after a short delay to ensure accurate measurements
    const initialCheckTimeout = setTimeout(() => {
      checkScrollButtons();
    }, 200);

    // Check on mount and after window resize
    checkScrollButtons();
    window.addEventListener('resize', checkScrollButtons);

    // Add scroll event listener to the container
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollButtons);
    }

    return () => {
      clearTimeout(initialCheckTimeout);
      window.removeEventListener('resize', checkScrollButtons);
      if (container) {
        container.removeEventListener('scroll', checkScrollButtons);
      }
    };
  }, []);

  // Scroll left function
  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      const cardWidth = 280 + 16; // Card width + gap
      scrollContainerRef.current.scrollBy({ left: -cardWidth * 2, behavior: 'smooth' });
    }
  };

  // Scroll right function
  const scrollRight = () => {
    if (scrollContainerRef.current) {
      const cardWidth = 280 + 16; // Card width + gap
      scrollContainerRef.current.scrollBy({ left: cardWidth * 2, behavior: 'smooth' });
    }
  };

  const handlePlanSelect = (planId: string) => {
    setSelectedPlan(planId)
    router.push(`/event/new/edit?plan=${planId}`)
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Header
        showUserProfile={true}
        buttons={session ? [
          {
            label: "Manage Events",
            onClick: () => router.push('/events'),
            variant: "outline"
          }
        ] : []}
      />
      <div className="container mx-auto px-4 py-8 flex-1">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-3">Choose the Right Event Plan</h1>
          <p className="text-lg text-gray-600">Select the option that fits your event size and needs</p>

          <div className="mt-4">
            <Badge className="px-3 py-1 bg-blue-100 text-blue-800">
              BETA MODE
            </Badge>
            <p className="mt-4 text-sm text-blue-600">
              During the Beta, you’ll get the $35 Host Pro plan for free on all your events.
            </p>
          </div>
        </div>

        {/* Horizontal scrolling plans container */}
        <div className="relative mb-6">
          {/* Scroll buttons - hidden on mobile screens */}
          {showLeftScroll && (
            <button 
              className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-2 shadow-md z-10 hover:bg-gray-100 border border-gray-200 focus:outline-none transition-all hidden md:block"
              onClick={scrollLeft}
              aria-label="Scroll left"
            >
              <ChevronLeft className="h-6 w-6 text-gray-700" />
            </button>
          )}
          {showRightScroll && (
            <button 
              className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-2 shadow-md z-10 hover:bg-gray-100 border border-gray-200 focus:outline-none transition-all hidden md:block"
              onClick={scrollRight}
              aria-label="Scroll right"
            >
              <ChevronRight className="h-6 w-6 text-gray-700" />
            </button>
          )}

          {/* Scrollable container */}
          <div className="flex overflow-x-auto pb-4 px-10 gap-6 hide-scrollbar xl:justify-center" ref={scrollContainerRef}>
            {plans.map((plan) => {
              // Check if the plan is coming soon or disabled
              const isComingSoon = plan.priceId === 'the_gatsby';
              const isDisabled = isComingSoon;
              
              return (
              <Card 
                key={plan.priceId}
                className={`relative flex-shrink-0 w-[250px] md:w-[280px] flex flex-col ${
                  selectedPlan === plan.priceId
                    ? "border-2 border-primary shadow-lg"
                    : isDisabled
                      ? "opacity-80 border-gray-200"
                      : ""
                }`}
              >
                {plan.ribbon && (
                  <Ribbon 
                    text={plan.ribbon.text}
                    backgroundColor={plan.ribbon.backgroundColor}
                    textColor={plan.ribbon.textColor}
                  />
                )}
                
                <CardHeader className="p-4 pb-2 text-center">
                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                  <CardDescription className="text-sm">{plan.description}</CardDescription>

                  {plan.priceId === 'host_pro' && (
                    <div className="mt-2 text-center">
                      <Badge className="bg-emerald-100 text-emerald-800 text-xs px-2 py-0.5">
                        Free During Beta
                      </Badge>
                    </div>
                  )}

                  <div className="mt-3 text-center">
                    {plan.priceId === 'the_gatsby' ? (
                      <div className="mt-2">
                        <span className="bg-white border border-rose-500 text-rose-500 rounded-full text-sm px-4 py-1 font-medium">
                          Coming Soon
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-baseline justify-center">
                        <span className="text-sm mr-1">A$</span>
                        <span className={`text-5xl font-bold ${plan.priceId === 'host_pro' ? 'line-through' : ''}`}>
                          {plan.priceId === 'free' ? '0' : plan.priceId === 'host_plus' ? '10' : '35'}
                        </span>
                        <span className="text-sm ml-1">/ Event</span>
                      </div>
                    )}
                  </div>

                  <div className="mt-4">
                    <Button
                      className="w-full"
                      variant={plan.priceId === 'host_pro' ? "primary-button" : "outline"}
                      onClick={() => handlePlanSelect(plan.priceId)}
                      disabled={plan.priceId === 'the_gatsby'}
                    >
                      {plan.priceId === 'the_gatsby' ? "Coming Soon" : "Get Started"}
                    </Button>
                  </div>
                </CardHeader>

                <CardContent className="p-4 pt-2 flex-grow overflow-y-auto">
                  {plan.includesText && (
                    <p className="font-semibold text-sm mb-2">{plan.includesText}</p>
                  )}
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="w-4 h-4 flex-shrink-0 flex items-center justify-center">
                          <Check className="h-4 w-4 text-green-500" />
                        </span>
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  {plan.comingSoonFeatures && plan.comingSoonFeatures.length > 0 && (
                    <div className="mt-4">
                      <div className="mb-2">
                        <p className="text-sm text-gray-500">Coming Soon Features</p>
                      </div>
                      <ul className="space-y-2">
                        {plan.comingSoonFeatures.map((feature, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="w-4 h-4 flex-shrink-0 flex items-center justify-center">
                              <Check className="h-4 w-4 text-gray-400" />
                            </span>
                            <span className="text-sm text-gray-500">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            )})}
          </div>
        </div>

      </div>
      <Footer type="marketing" />
    </div>
  )
}