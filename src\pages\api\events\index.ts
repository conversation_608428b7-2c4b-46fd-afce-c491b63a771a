import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { GenerateID } from '@/lib/ID';
import { sendEventWelcomeEmail } from '@/lib/mailer';
import { log } from '@/lib/logger';
import { Event, EventListItem } from '@/types';

/**
 * Handles GET requests for fetching a user's events
 * 
 * This endpoint retrieves all events owned by the authenticated user
 * as well as events where the user is listed as a manager.
 * It transforms the raw database events into the EventListItem format,
 * including payment and plan information for display in the UI.
 * 
 * @param req - Next.js API request object
 * @param res - Next.js API response object
 * @returns HTTP response with the list of events or error status
 */
async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authConfig);

    if (!session?.user?.id) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    log('Fetching events for user:', session.user.id);
    const db = Database.getInstance();

    // Fetch events where user is the owner by account ID
    const ownedEventsByAccountId = await db.ListData('events', {
      field: 'ownerAccountId',
      operator: '==',
      value: session.user.id
    }) as Event[];

    // For backward compatibility, also fetch events where user is the owner by email
    const ownedEventsByEmail = await db.ListData('events', {
      field: 'ownerEmail',
      operator: '==',
      value: session.user.email
    }) as Event[];

    // Combine owned events, removing duplicates
    const ownedEvents = [...ownedEventsByAccountId];
    ownedEventsByEmail.forEach(event => {
      if (!ownedEvents.some(e => e.ID === event.ID)) {
        ownedEvents.push(event);
      }
    });

    // Fetch events where user is listed as a manager by account ID
    const managedEvents = await db.ListData('events', {
      field: 'managers',
      operator: 'array-contains',
      value: session.user.id
    }) as Event[];

    // Combine both arrays and remove duplicates (in case user is both owner and manager)
    const allEvents = [...ownedEvents];

    // Add managed events that aren't already in the owned events list
    for (const event of managedEvents) {
      if (!allEvents.some(e => e.ID === event.ID)) {
        allEvents.push(event);
      }
    }

    log('Found ' + allEvents.length + ' events in total');

    // Transform the events to match the expected format
    const transformedEvents: EventListItem[] = allEvents.map((event: Event) => ({
      id: event.ID || GenerateID('E'), // Ensure ID is always a string
      name: event.eventName,
      date: event.eventDate ? new Date(event.eventDate) : null,
      start: event.start,
      end: event.end,
      location: event.location,
      timezone: event.timezone,
      message: event.message,
      host: event.host,
      ownerAccountId: event.ownerAccountId || '',
      ownerEmail: event.ownerEmail,      guestCount: 0, // @TODO: Implement guest count logic
      plan: event.plan,
      paymentStatus: event.paymentStatus,
      status: event.status,
      isManager: (event.ownerAccountId !== session.user.id) && (event.ownerEmail !== session.user.email),
      organizationId: event.organizationId,
      raw: event
    }));

    return res.status(200).json(transformedEvents);
  } catch (error) {
    console.error('Error fetching events:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authConfig);

    if (!session?.user?.id) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const event = req.body;
    event.ID = GenerateID('E');
    event.ownerAccountId = session.user.id;
    event.ownerEmail = session.user.email; // Keep for backward compatibility

    // Initialize managers array if not provided
    if (!event.managers) {
      event.managers = [];
    }

    const response = await Database.getInstance().addData('events', event);

    // Send welcome email to the event host
    await sendEventWelcomeEmail(event, {
      name: event.host,
      email: event.ownerEmail
    });

    return res.status(200).json({
      ...event
    });
  } catch (error) {
    console.error('Error creating event:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'GET':
      return handleGet(req, res);
    case 'POST':
      return handlePost(req, res);
    default:
      return res.status(405).json({ message: 'Method not allowed' });
  }
}