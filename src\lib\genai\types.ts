/**
 * TypeScript interfaces and types for GenAI service
 */

export interface GenAIConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  topK: number;
}

export interface GenAIUsageCounters {
  digitalInviteGenerations: number;
  printableInviteGenerations: number;
  descriptionGenerations: number;
  totalGenerations: number;
  lastGenerationDate: Date;
}

export interface GenAIRateLimit {
  userId: string;
  dailyLimit: number;
  currentUsage: number;
  resetTime: Date;
  userType: 'free' | 'paid' | 'admin';
}

export interface GenAIGenerationRequest {
  eventId: string;
  userId: string;
  type: 'digital-invite' | 'printable-invite' | 'event-description';
  context: EventContext;
  options?: GenerationOptions;
}

export interface EventContext {
  eventName: string;
  eventDate: Date;
  start: string;
  end: string;
  location: string;
  timezone?: string;
  message: string;
  host: string;
  rsvpDueDate?: Date;
}

export interface GenerationOptions {
  // Digital invite options
  dimensions?: {
    width: number;
    height: number;
  };
  theme?: 'modern' | 'classic' | 'elegant' | 'fun' | 'minimal';
  colorScheme?: 'warm' | 'cool' | 'neutral' | 'vibrant';
  
  // Printable invite options
  paperSize?: 'A4' | 'A5' | 'A6' | 'photo4x6' | 'photo5x7' | 'photo6x8' | 'photo8x10';
  orientation?: 'portrait' | 'landscape';
  
  // Event description options
  tone?: 'formal' | 'casual' | 'friendly' | 'professional' | 'exciting';
  length?: 'short' | 'medium' | 'long';
  includeDetails?: boolean;
}

export interface GenAIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  usage?: {
    tokensUsed: number;
    generationTime: number;
    remainingQuota: number;
  };
}

export interface DigitalInviteResult {
  svgContent: string;
  imageBuffer?: Buffer;
  dimensions: {
    width: number;
    height: number;
  };
  format: 'svg' | 'png' | 'jpeg';
}

export interface PrintableInviteResult {
  svgContent: string;
  imageBuffer?: Buffer;
  dimensions: {
    width: number;
    height: number;
  };
  paperSize: string;
  orientation: 'portrait' | 'landscape';
  format: 'svg' | 'png' | 'jpeg' | 'pdf';
}

export interface EventDescriptionResult {
  title: string;
  description: string;
  shortDescription: string;
  keywords: string[];
  tone: string;
}

export interface GenAIError {
  code: string;
  message: string;
  details?: any;
}

export interface UsageStats {
  userId: string;
  eventId?: string;
  totalGenerations: number;
  generationsByType: {
    digitalInvite: number;
    printableInvite: number;
    eventDescription: number;
  };
  dailyUsage: number;
  remainingQuota: number;
  resetTime: Date;
}

// Prompt template types
export interface PromptTemplate {
  system: string;
  user: string;
  examples?: PromptExample[];
}

export interface PromptExample {
  input: string;
  output: string;
}

export interface PromptContext {
  eventContext: EventContext;
  options: GenerationOptions;
  userPreferences?: any;
}

// Generation result metadata
export interface GenerationMetadata {
  generatedAt: Date;
  generationType: 'digital-invite' | 'printable-invite' | 'event-description';
  modelUsed: string;
  tokensUsed: number;
  generationTime: number;
  userId: string;
  eventId: string;
}
