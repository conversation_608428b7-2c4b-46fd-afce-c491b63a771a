'use client'

import Link from "next/link"
import Image from "next/image"
import { Separator } from "@/components/ui/separator"

type FooterProps = {
  type: 'marketing' | 'app'
  className?: string
}

export function Footer({ type = 'marketing', className = '' }: FooterProps) {
  const currentYear = new Date().getFullYear()

  if (type === 'app') {
    // Minimal footer for authenticated/app pages
    return (
      <footer className={`w-full border-t mt-auto print:hidden py-4 bg-white ${className}`}>
        <div className="container mx-auto px-4">
          <div className="flex flex-col items-center justify-center gap-2">
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <Link href="/terms" className="hover:text-primary">Terms of Use</Link>
              <span>•</span>
              <Link href="/privacy" className="hover:text-primary">Privacy Policy</Link>
              <span>•</span>
              <Link href="/data-governance" className="hover:text-primary">Data Governance</Link>
            </div>
            <p className="text-xs text-gray-400">
              © {currentYear} I am Coming. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    )
  }

  // Full marketing footer
  return (
    <footer className={`w-full border-t mt-auto print:hidden bg-gray-50 ${className}`}>
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <Link href="/" className="inline-block">
              <Image 
                src="/iac-logo.svg"
                alt="I am Coming - Event RSVP Management Platform" 
                width={140} 
                height={40}
                className="h-10 w-auto"
              />
            </Link>
            <p className="text-sm text-gray-600">
              Your simple event RSVP management platform
            </p>
            <div className="flex space-x-4">
              <a 
                href="https://www.facebook.com/app.iamcoming.io/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-primary transition-colors"
              >
                <Image src="/Facebook.svg" alt="Facebook" width={20} height={20} />
                <span className="sr-only">Facebook</span>
              </a>
              <a
                href="https://twitter.com/iamcoming_io"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-primary transition-colors"
              >
                <Image src="/X.svg" alt="X" width={20} height={20} />
                <span className="sr-only">X</span>
              </a>
              <a 
                href="https://www.instagram.com/iamcoming.io/" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="text-gray-500 hover:text-primary transition-colors"
              >
                <Image src="/Instagram.svg" alt="Instagram" width={20} height={20} />
                <span className="sr-only">Instagram</span>
              </a>
              <a 
                href="https://www.linkedin.com/company/iamcoming/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-primary transition-colors"
              >
                <Image src="/LinkedIn.svg" alt="LinkedIn" width={20} height={20} />
                <span className="sr-only">LinkedIn</span>
              </a>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-4">Platform</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-600 hover:text-primary">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/pricing" className="text-gray-600 hover:text-primary">
                  Pricing
                </Link>
              </li>
              <li>
                <Link href="/events" className="text-gray-600 hover:text-primary">
                  Events Dashboard
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-4">Legal</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/terms" className="text-gray-600 hover:text-primary">
                  Terms of Use
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-gray-600 hover:text-primary">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/data-governance" className="text-gray-600 hover:text-primary">
                  Data Governance
                </Link>
              </li>
              <li>
                <Link href="/cookies" className="text-gray-600 hover:text-primary">
                  Cookie Policy
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-4">Company</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-gray-600 hover:text-primary">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-600 hover:text-primary">
                  Contact
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <Separator className="my-6" />
        
        <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-500">
              © {currentYear} I am Coming. All rights reserved. | {process.env.NEXT_PUBLIC_BUILDENV_VERSION || 'dev'}
            </p>
          <div className="mt-4 md:mt-0 flex space-x-4 text-sm text-gray-500">
            <Link href="/terms" className="hover:text-primary">Terms</Link>
            <Link href="/privacy" className="hover:text-primary">Privacy</Link>
            <Link href="/cookies" className="hover:text-primary">Cookies</Link>
          </div>
        </div>
      </div>
    </footer>
  )
}