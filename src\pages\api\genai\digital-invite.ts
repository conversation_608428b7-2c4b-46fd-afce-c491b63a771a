/**
 * API endpoint for generating digital invites using GenAI
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { getGenAIService } from '@/lib/genai';
import { Database } from '@/lib/database';
import { log } from '@/lib/logger';
import { rateLimit } from '@/lib/rateLimiter';
import { GenAIGenerationRequest, EventContext } from '@/lib/genai/types';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Apply basic rate limiting (10 requests per minute per IP)
  if (!rateLimit(req, res, 10, 60 * 1000)) {
    return res.status(429).json({
      error: 'Too many requests, please try again later',
      retryAfter: res.getHeader('Retry-After')
    });
  }

  try {
    // Check authentication
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Extract request data
    const { eventId, context, options = {} } = req.body;

    if (!eventId) {
      return res.status(400).json({ error: 'Event ID is required' });
    }

    if (!context) {
      return res.status(400).json({ error: 'Event context is required' });
    }

    // Get event data from database or use test data
    const db = Database.getInstance();
    let event = null;

    // Check if this is a test event ID
    if (eventId.startsWith('test-event-')) {
      // For test events, we'll use the context data directly
      // and skip permission checks
      event = {
        ID: eventId,
        ownerAccountId: session.user.id,
        managers: [],
        // We'll use the context data passed in the request
      };
    } else {
      // For real events, get from database and check permissions
      event = await db.readData('events', eventId);

      if (!event) {
        return res.status(404).json({ error: 'Event not found' });
      }

      // Check if user has permission to generate for this event
      const isOwner = event.ownerAccountId === session.user.id;
      const isManager = event.managers && event.managers.includes(session.user.id);

      if (!isOwner && !isManager) {
        return res.status(403).json({ error: 'Permission denied' });
      }
    }

    // Prepare event context - use provided context for test events, or event data for real events
    const eventContext: EventContext = eventId.startsWith('test-event-') ? context : {
      eventName: event.eventName,
      eventDate: new Date(event.eventDate),
      start: event.start,
      end: event.end,
      location: event.location,
      timezone: event.timezone,
      message: event.message,
      host: event.host,
      rsvpDueDate: event.rsvpDueDate ? new Date(event.rsvpDueDate) : undefined,
    };

    // Prepare generation request
    const generationRequest: GenAIGenerationRequest = {
      eventId,
      userId: session.user.id,
      type: 'digital-invite',
      context: eventContext,
      options: {
        dimensions: options.dimensions || { width: 800, height: 600 },
        theme: options.theme || 'modern',
        colorScheme: options.colorScheme || 'neutral',
        ...options,
      },
    };

    // Generate digital invite
    const genAIService = getGenAIService();
    const result = await genAIService.generateDigitalInvite(generationRequest);

    if (!result.success) {
      log('Digital invite generation failed', { 
        eventId, 
        userId: session.user.id, 
        error: result.error 
      });
      
      return res.status(400).json({ 
        error: result.error || 'Failed to generate digital invite' 
      });
    }

    log('Digital invite generated successfully', { 
      eventId, 
      userId: session.user.id,
      usage: result.usage 
    });

    return res.status(200).json({
      success: true,
      data: result.data,
      usage: result.usage,
    });

  } catch (error) {
    console.error('Digital invite generation error:', error);
    log('Digital invite generation error', { error });
    
    return res.status(500).json({ 
      error: 'Internal server error' 
    });
  }
}

// Configure API route
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
};
