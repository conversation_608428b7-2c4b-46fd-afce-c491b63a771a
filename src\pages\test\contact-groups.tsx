import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { AdminLayout } from '@/components/layouts/AdminLayout';
import { withAdminAuth } from '@/lib/auth/admin';
import { Loader2, Mail, Calendar, Users, TestTube, Play } from 'lucide-react';
import { GetServerSideProps } from 'next';

export default function ContactGroupsTestPage() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [eventId, setEventId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);
  const [isBulkRunning, setIsBulkRunning] = useState(false);
  const [bulkResults, setBulkResults] = useState<any>(null);

  const handleCheckEvent = async () => {
    if (!eventId.trim()) {
      toast({
        title: "Event ID required",
        description: "Please enter an event ID to check",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    setTestResult(null);

    try {
      const response = await fetch(`/api/test/check-event-groups?eventId=${encodeURIComponent(eventId.trim())}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to check event');
      }

      setTestResult({ ...data, type: 'check' });

      toast({
        title: "Event checked",
        description: `Found ${data.savableGroups.length} savable contact groups`,
      });

    } catch (error) {
      console.error('Error checking event:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to check event",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTriggerCompletion = async (forceExpire: boolean = false) => {
    if (!eventId.trim()) {
      toast({
        title: "Event ID required",
        description: "Please enter an event ID to test",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    setTestResult(null);

    try {
      const response = await fetch('/api/test/trigger-event-completion', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          eventId: eventId.trim(),
          forceExpire
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to trigger event completion');
      }

      setTestResult({ ...data, type: 'trigger' });

      toast({
        title: "Success!",
        description: `Event completion triggered. Email sent to ${data.emailSentTo}`,
      });

    } catch (error) {
      console.error('Error triggering event completion:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to trigger event completion",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runBulkCompletedEventsCheck = async () => {
    setIsBulkRunning(true);
    setBulkResults(null);

    try {
      const response = await fetch('/api/test/check-completed-events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to run bulk test');
      }

      const data = await response.json();
      setBulkResults(data);

      toast({
        title: "Bulk test completed!",
        description: `Processed ${data.processedCount || 0} events, sent ${data.emailsSent || 0} emails, ${data.errorCount || 0} errors`,
      });

    } catch (error) {
      console.error('Error running bulk test:', error);
      toast({
        title: "Bulk test failed",
        description: error instanceof Error ? error.message : "Failed to run completed events check",
        variant: "destructive",
      });
    } finally {
      setIsBulkRunning(false);
    }
  };

  return (
    <AdminLayout pageTitle="Contact Groups Test">
      <div className="space-y-6">

          {/* Test Instructions */}
          <Card className="mb-6 bg-white shadow-sm">
            <CardHeader>
              <CardTitle>📋 Testing Instructions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <h3 className="font-semibold text-blue-900 mb-2">Step 1: Create Test Event</h3>
                  <ol className="list-decimal list-inside space-y-1 text-blue-800 text-sm">
                    <li>Create a new event</li>
                    <li>Add invites with different group names (e.g., &quot;Family&quot;, &quot;Friends&quot;, &quot;Colleagues&quot;)</li>
                    <li>Make sure invites have email addresses</li>
                    <li>Copy the event ID from the URL</li>
                  </ol>
                </div>

                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <h3 className="font-semibold text-green-900 mb-2">Step 2: Test Ultra-Simple Email Logic</h3>
                  <ol className="list-decimal list-inside space-y-1 text-green-800 text-sm">
                    <li>Use &quot;Check Event Groups&quot; to see if organization qualifies for email</li>
                    <li><strong>Key Test:</strong> Email sent ONLY if <code>hasBeenAsked = false</code></li>
                    <li>Click &quot;Force Expire & Send Email&quot; to send ONE-TIME introduction email</li>
                    <li>After email sent, organization marked <code>hasBeenAsked = true</code> FOREVER</li>
                  </ol>
                </div>

                <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                  <h3 className="font-semibold text-purple-900 mb-2">🧪 Ultra-Simple Test Cases</h3>
                  <div className="space-y-2 text-purple-800 text-sm">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <strong>New Organization:</strong> <code>hasBeenAsked = false</code> → ✅ Gets email
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                      <strong>Contacted Organization:</strong> <code>hasBeenAsked = true</code> → ❌ No email
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-gray-500 rounded-full"></span>
                      <strong>No Groups:</strong> Event has no contact groups → ❌ No email
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Bulk Test Controls */}
          <Card className="mb-6 bg-white shadow-sm">
            <CardHeader>
              <CardTitle>🔄 Bulk Completed Events Check</CardTitle>
              <CardDescription>
                Simulates the cron job that finds expired events (24+ hours old) and marks them as completed, triggering contact group emails
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h3 className="font-semibold text-blue-900 mb-2">🧪 What This Test Does:</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• <strong>Checks test event:</strong> E2i8pe45d (only this event, won&apos;t affect real users)</li>
                  <li>• <strong>Finds expired events:</strong> Events 24+ hours past their date</li>
                  <li>• <strong>Marks as completed:</strong> Changes status from &quot;active&quot; to &quot;completed&quot;</li>
                  <li>• <strong>Triggers email logic:</strong> Sends introduction email if <code>hasBeenAsked = false</code></li>
                  <li>• <strong>Same as production cron:</strong> Identical logic to the real cron job</li>
                  <li>• <strong>Safe for testing:</strong> Only processes designated test events</li>
                </ul>
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h3 className="font-semibold text-yellow-900 mb-2">📋 Step-by-Step Process:</h3>
                <ol className="text-sm text-yellow-800 space-y-1 list-decimal list-inside">
                  <li><strong>Check Event Date:</strong> Is the event 24+ hours past its scheduled date?</li>
                  <li><strong>Check Status:</strong> Is the event still marked as &quot;active&quot; (not already completed)?</li>
                  <li><strong>Mark Completed:</strong> Update event status to &quot;completed&quot; with timestamp</li>
                  <li><strong>Check Organization:</strong> Has this organization been asked before? (<code>hasBeenAsked</code>)</li>
                  <li><strong>Check Groups:</strong> Does the event have savable contact groups?</li>
                  <li><strong>Send Email:</strong> If organization never asked + has groups → send introduction email</li>
                  <li><strong>Update Settings:</strong> Mark organization as <code>hasBeenAsked = true</code> (forever)</li>
                </ol>
              </div>

              <Button
                onClick={runBulkCompletedEventsCheck}
                disabled={isBulkRunning}
                className="w-full"
                size="lg"
              >
                {isBulkRunning ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Running Bulk Check...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Run Bulk Completed Events Check
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Individual Test Controls */}
          <Card className="mb-6 bg-white shadow-sm">
            <CardHeader>
              <CardTitle>🧪 Individual Event Test</CardTitle>
              <CardDescription>
                Enter an event ID and trigger the post-event contact group save flow
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="eventId" className="text-sm font-medium">Event ID</Label>
                <Input
                  id="eventId"
                  placeholder="E123456789"
                  value={eventId}
                  onChange={(e) => setEventId(e.target.value)}
                  className="mt-1"
                />
                <p className="text-sm text-gray-500 mt-1">
                  Copy the event ID from your event URL (e.g., /event/E123456789/edit)
                </p>
              </div>

              <div className="flex flex-wrap gap-3">
                <Button
                  onClick={handleCheckEvent}
                  disabled={isLoading || !eventId.trim()}
                  variant="outline"
                  size="sm"
                >
                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Users className="h-4 w-4 mr-2" />}
                  Check Event Groups
                </Button>

                <Button
                  onClick={() => handleTriggerCompletion(false)}
                  disabled={isLoading || !eventId.trim()}
                  variant="outline"
                  size="sm"
                >
                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Calendar className="h-4 w-4 mr-2" />}
                  Test Trigger (No Changes)
                </Button>

                <Button
                  onClick={() => handleTriggerCompletion(true)}
                  disabled={isLoading || !eventId.trim()}
                  variant="primary-button"
                  size="sm"
                >
                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Mail className="h-4 w-4 mr-2" />}
                  Force Expire & Send Email
                </Button>
              </div>

              <div className="text-sm text-gray-500 space-y-1 bg-gray-50 p-3 rounded-lg">
                <p><strong>Check Event Groups:</strong> Shows contact groups and organization email status</p>
                <p><strong>Test Trigger:</strong> Tests the trigger logic without changing event date</p>
                <p><strong>Force Expire & Send Email:</strong> Sets event date to past and sends ONE-TIME introduction email</p>
                <p className="text-xs text-orange-600 mt-2">⚠️ After sending email, organization marked as &quot;asked&quot; forever</p>
              </div>
            </CardContent>
          </Card>

          {/* Bulk Test Results */}
          {bulkResults && (
            <Card className="mb-6 bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Play className="h-5 w-5" />
                  Bulk Test Results
                </CardTitle>
                <CardDescription>
                  {bulkResults.message}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <div className="bg-gray-50 p-3 rounded-lg text-center">
                    <div className="text-2xl font-bold text-gray-900">{bulkResults.totalEvents || 0}</div>
                    <div className="text-sm text-gray-600">Total Events</div>
                  </div>
                  <div className="bg-blue-50 p-3 rounded-lg text-center">
                    <div className="text-2xl font-bold text-blue-900">{bulkResults.processedCount || 0}</div>
                    <div className="text-sm text-blue-600">Processed</div>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg text-center">
                    <div className="text-2xl font-bold text-green-900">{bulkResults.emailsSent || 0}</div>
                    <div className="text-sm text-green-600">Emails Sent</div>
                  </div>
                  <div className="bg-red-50 p-3 rounded-lg text-center">
                    <div className="text-2xl font-bold text-red-900">{bulkResults.errorCount || 0}</div>
                    <div className="text-sm text-red-600">Errors</div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-semibold">Event Details:</h4>
                  {bulkResults.results.map((result: any, index: number) => (
                    <div key={index} className="border rounded-lg p-3 bg-white">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{result.eventName}</span>
                        <Badge className={
                          result.status === 'email_sent' ? 'bg-green-100 text-green-800' :
                          result.status === 'processed' ? 'bg-blue-100 text-blue-800' :
                          result.status === 'error' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }>
                          {result.status.replace('_', ' ')}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600">
                        <div>Event ID: {result.eventId}</div>
                        <div>Message: {result.message}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Individual Test Results */}
          {testResult && (
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {testResult.type === 'check' ? <Users className="h-5 w-5" /> : <Mail className="h-5 w-5" />}
                  {testResult.type === 'check' ? 'Event Analysis' : 'Test Results'}
                </CardTitle>
              </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Event</Label>
                    <p className="text-sm">{testResult.eventName}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Organization</Label>
                    <p className="text-sm">{testResult.organizationName}</p>
                  </div>
                  {testResult.emailSentTo && (
                    <div>
                      <Label className="text-sm font-medium">Email Sent To</Label>
                      <p className="text-sm">{testResult.emailSentTo}</p>
                    </div>
                  )}
                  {testResult.type === 'check' && (
                    <>
                      <div>
                        <Label className="text-sm font-medium">Event Status</Label>
                        <Badge variant={testResult.isPastDue ? "destructive" : "secondary"}>
                          {testResult.isPastDue ? "Past Due" : "Active"}
                        </Badge>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Total Invites</Label>
                        <p className="text-sm">{testResult.totalInvites}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Can Send Email</Label>
                        <Badge variant={testResult.canTriggerEmail?.shouldSendEmail && testResult.canTriggerEmail?.hasValidGroups ? "default" : "secondary"}>
                          {testResult.canTriggerEmail?.shouldSendEmail && testResult.canTriggerEmail?.hasValidGroups ? "Yes" : "No"}
                        </Badge>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Organization Email Status</Label>
                        <Badge variant={testResult.canTriggerEmail?.shouldSendEmail ? "default" : "destructive"}>
                          {testResult.canTriggerEmail?.shouldSendEmail ? "Never Asked (hasBeenAsked = false)" : "Already Asked (hasBeenAsked = true)"}
                        </Badge>
                      </div>
                    </>
                  )}
                </div>

                {/* Organization Settings - Simplified Display */}
                {testResult.type === 'check' && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">🏢 Organization Contact Group Settings</Label>
                    <div className="bg-gray-50 p-3 rounded border">
                      {testResult.canTriggerEmail?.organizationSettings ? (
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="font-medium">hasBeenAsked:</span>
                            <Badge variant={testResult.canTriggerEmail.organizationSettings.hasBeenAsked ? "destructive" : "default"}>
                              {testResult.canTriggerEmail.organizationSettings.hasBeenAsked ? "true" : "false"}
                            </Badge>
                          </div>
                          <div className="text-xs text-gray-600 mt-2 p-2 bg-white rounded">
                            {testResult.canTriggerEmail.organizationSettings.hasBeenAsked
                              ? "🚫 Organization already received introduction email - no more emails will be sent"
                              : "✅ Organization eligible for one-time introduction email"
                            }
                          </div>
                        </div>
                      ) : (
                        <div className="text-sm text-gray-600">
                          <Badge variant="default">No settings (hasBeenAsked = false)</Badge>
                          <p className="text-xs mt-1 p-2 bg-white rounded">✅ Organization eligible for one-time introduction email</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Event Details</Label>
                    <div className="space-y-1 text-sm">
                      <p><strong>Event:</strong> {testResult.eventName}</p>
                      <p><strong>Date:</strong> {new Date(testResult.eventDate).toLocaleDateString()}</p>
                      <p><strong>Organization:</strong> {testResult.organizationName}</p>
                      <p><strong>Owner:</strong> {testResult.ownerEmail}</p>
                    </div>
                  </div>
                  {testResult.type === 'trigger' && (
                    <div>
                      <Label className="text-sm font-medium">Email Sent To</Label>
                      <p className="text-sm">{testResult.emailSentTo}</p>
                    </div>
                  )}
                  {testResult.type === 'check' && (
                    <>
                    </>
                  )}
                  {testResult.forceExpired !== undefined && (
                    <div>
                      <Label className="text-sm font-medium">Force Expired</Label>
                      <Badge variant={testResult.forceExpired ? "destructive" : "secondary"}>
                        {testResult.forceExpired ? "Yes" : "No"}
                      </Badge>
                    </div>
                  )}
                </div>

                {/* Contact Groups */}
                {testResult.contactGroups && testResult.contactGroups.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">
                      {testResult.type === 'check' ? 'All Contact Groups' : 'Contact Groups Found'}
                    </Label>
                    <div className="space-y-2">
                      {testResult.contactGroups.map((group: any, index: number) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex items-center gap-2">
                            <Badge variant={group.name === 'Ungrouped' ? "secondary" : "outline"}>
                              {group.name}
                            </Badge>
                            <span className="text-sm text-gray-600">
                              {group.contactCount} contacts
                            </span>
                          </div>
                          {testResult.type === 'check' && (
                            <Badge variant={group.hasValidContacts ? "default" : "secondary"} className="text-xs">
                              {group.hasValidContacts ? "Savable" : "No valid contacts"}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Savable Groups for check results */}
                {testResult.type === 'check' && testResult.savableGroups && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Savable Groups</Label>
                    <p className="text-sm text-gray-600 mb-2">
                      {testResult.savableGroups.length} groups can be saved (excluding &apos;Ungrouped&apos; and groups without valid contacts)
                    </p>
                    {testResult.savableGroups.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {testResult.savableGroups.map((group: any, index: number) => (
                          <Badge key={index} variant="default" className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            {group.name} ({group.contactCount})
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Next Steps */}
                {testResult.type === 'trigger' && (
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-green-900 mb-2">✅ Next Steps</h4>
                    <ol className="list-decimal list-inside space-y-1 text-green-800 text-sm">
                      <li>Check your email inbox for the contact group save request</li>
                      <li>Click &quot;Save These Groups&quot; to test the save flow</li>
                      <li>Or click &quot;No Thanks&quot; to test the decline flow</li>
                      <li>Verify the contact groups appear in your organization&apos;s saved groups</li>
                    </ol>
                  </div>
                )}

                {testResult.type === 'check' && testResult.savableGroups?.length > 0 && testResult.canTriggerEmail?.shouldSendEmail && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-900 mb-2">🚀 Ready to Test One-Time Email</h4>
                    <p className="text-blue-800 text-sm mb-2">
                      This event has {testResult.savableGroups.length} savable contact groups and the organization has never been asked.
                      Click &quot;Force Expire & Send Email&quot; to test the ONE-TIME introduction email flow.
                    </p>
                    <p className="text-blue-700 text-xs">
                      ⚠️ After this test, the organization will be marked as &quot;asked&quot; and won&apos;t receive any more emails.
                    </p>
                  </div>
                )}

                {testResult.type === 'check' && testResult.savableGroups?.length > 0 && !testResult.canTriggerEmail?.shouldSendEmail && (
                  <div className="bg-orange-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-orange-900 mb-2">🚫 Organization Already Asked</h4>
                    <p className="text-orange-800 text-sm mb-2">
                      This event has {testResult.savableGroups.length} savable contact groups, but the organization has already received the introduction email.
                    </p>
                    <p className="text-orange-700 text-xs">
                      ✅ This is the expected behavior - organizations only get ONE introduction email ever.
                    </p>
                  </div>
                )}

                {testResult.type === 'check' && testResult.savableGroups?.length === 0 && (
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-yellow-900 mb-2">⚠️ No Savable Groups</h4>
                    <p className="text-yellow-800 text-sm">
                      This event doesn&apos;t have any savable contact groups. Add some invites with group names and email addresses to test the feature.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

          {/* Quick Links */}
          <Card className="mt-6 bg-white shadow-sm">
            <CardHeader>
              <CardTitle>🔗 Quick Links</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  variant="outline"
                  onClick={() => window.open('/events', '_blank')}
                  className="justify-start"
                >
                  📅 View My Events
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.open('/event/new', '_blank')}
                  className="justify-start"
                >
                  ➕ Create New Event
                </Button>
              </div>
            </CardContent>
          </Card>
      </div>
    </AdminLayout>
  );
}

// Server-side protection for admin routes
export const getServerSideProps: GetServerSideProps = withAdminAuth();
