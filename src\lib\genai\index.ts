/**
 * Main GenAI service class for generating digital invites, printable invites, and event descriptions
 */

import { log } from '@/lib/logger';
import { Database } from '@/lib/database';
import { GenAIRateLimiter } from './rate-limiter';
import { DigitalInviteGenerator } from './digital-invite';
import { PrintableInviteGenerator } from './printable-invite';
import { EventDescriptionGenerator } from './event-description';
import {
  GenAIConfig,
  GenAIGenerationRequest,
  GenAIResponse,
  DigitalInviteResult,
  PrintableInviteResult,
  EventDescriptionResult,
  GenerationMetadata,
} from './types';
import {
  DEFAULT_GENAI_CONFIG,
  ERROR_CODES,
  GENERATION_CONFIG,
  CACHE_KEYS,
  DB_COLLECTIONS,
} from './config';

export class GenAIService {
  private config: GenAIConfig;
  private rateLimiter: GenAIRateLimiter;
  private db: Database;
  private cache: Map<string, any> = new Map();
  private digitalInviteGenerator: DigitalInviteGenerator;
  private printableInviteGenerator: PrintableInviteGenerator;
  private eventDescriptionGenerator: EventDescriptionGenerator;

  constructor(config?: Partial<GenAIConfig>) {
    this.config = { ...DEFAULT_GENAI_CONFIG, ...config };

    if (!this.config.apiKey) {
      throw new Error('Gemini API key is required');
    }

    this.rateLimiter = new GenAIRateLimiter();
    this.db = Database.getInstance();

    // Initialize generators
    this.digitalInviteGenerator = new DigitalInviteGenerator(this.config);
    this.printableInviteGenerator = new PrintableInviteGenerator(this.config);
    this.eventDescriptionGenerator = new EventDescriptionGenerator(this.config);

    log('GenAI service initialized', { model: this.config.model });
  }

  /**
   * Generate digital invite image
   */
  async generateDigitalInvite(request: GenAIGenerationRequest): Promise<GenAIResponse<DigitalInviteResult>> {
    try {
      // Check rate limits
      const rateLimitCheck = await this.rateLimiter.checkAccess(request.userId);
      if (!rateLimitCheck.allowed) {
        return {
          success: false,
          error: rateLimitCheck.error,
        };
      }

      // Validate input
      const validation = this.validateGenerationRequest(request);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      // Check cache
      const cacheKey = CACHE_KEYS.GENERATION(request.eventId, 'digital-invite');
      if (this.cache.has(cacheKey)) {
        log('Returning cached digital invite', { eventId: request.eventId });
        return {
          success: true,
          data: this.cache.get(cacheKey),
        };
      }

      const startTime = Date.now();

      // Generate the invite (implementation will be added in Phase 3)
      const result = await this.generateDigitalInviteInternal(request);

      const generationTime = Date.now() - startTime;

      // Save generation metadata
      await this.saveGenerationMetadata({
        generatedAt: new Date(),
        generationType: 'digital-invite',
        modelUsed: this.config.model,
        tokensUsed: 0, // Will be updated when we get actual token usage
        generationTime,
        userId: request.userId,
        eventId: request.eventId,
      });

      // Cache the result
      this.cache.set(cacheKey, result);
      setTimeout(() => this.cache.delete(cacheKey), GENERATION_CONFIG.cacheTimeout);

      return {
        success: true,
        data: result,
        usage: {
          tokensUsed: 0, // Will be updated
          generationTime,
          remainingQuota: rateLimitCheck.remaining,
        },
      };

    } catch (error) {
      log('Digital invite generation failed', { error, request });
      return {
        success: false,
        error: this.formatError(error),
      };
    }
  }

  /**
   * Generate printable invite image
   */
  async generatePrintableInvite(request: GenAIGenerationRequest): Promise<GenAIResponse<PrintableInviteResult>> {
    try {
      // Check rate limits
      const rateLimitCheck = await this.rateLimiter.checkAccess(request.userId);
      if (!rateLimitCheck.allowed) {
        return {
          success: false,
          error: rateLimitCheck.error,
        };
      }

      // Validate input
      const validation = this.validateGenerationRequest(request);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      const startTime = Date.now();

      // Generate the invite (implementation will be added in Phase 4)
      const result = await this.generatePrintableInviteInternal(request);

      const generationTime = Date.now() - startTime;

      // Save generation metadata
      await this.saveGenerationMetadata({
        generatedAt: new Date(),
        generationType: 'printable-invite',
        modelUsed: this.config.model,
        tokensUsed: 0,
        generationTime,
        userId: request.userId,
        eventId: request.eventId,
      });

      return {
        success: true,
        data: result,
        usage: {
          tokensUsed: 0,
          generationTime,
          remainingQuota: rateLimitCheck.remaining,
        },
      };

    } catch (error) {
      log('Printable invite generation failed', { error, request });
      return {
        success: false,
        error: this.formatError(error),
      };
    }
  }

  /**
   * Generate event description
   */
  async generateEventDescription(request: GenAIGenerationRequest): Promise<GenAIResponse<EventDescriptionResult>> {
    try {
      // Check rate limits
      const rateLimitCheck = await this.rateLimiter.checkAccess(request.userId);
      if (!rateLimitCheck.allowed) {
        return {
          success: false,
          error: rateLimitCheck.error,
        };
      }

      // Validate input
      const validation = this.validateGenerationRequest(request);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      const startTime = Date.now();

      // Generate the description (implementation will be added in Phase 5)
      const result = await this.generateEventDescriptionInternal(request);

      const generationTime = Date.now() - startTime;

      // Save generation metadata
      await this.saveGenerationMetadata({
        generatedAt: new Date(),
        generationType: 'event-description',
        modelUsed: this.config.model,
        tokensUsed: 0,
        generationTime,
        userId: request.userId,
        eventId: request.eventId,
      });

      return {
        success: true,
        data: result,
        usage: {
          tokensUsed: 0,
          generationTime,
          remainingQuota: rateLimitCheck.remaining,
        },
      };

    } catch (error) {
      log('Event description generation failed', { error, request });
      return {
        success: false,
        error: this.formatError(error),
      };
    }
  }

  /**
   * Validate generation request
   */
  private validateGenerationRequest(request: GenAIGenerationRequest): { valid: boolean; error?: string } {
    if (!request.eventId || !request.userId) {
      return { valid: false, error: 'Event ID and User ID are required' };
    }

    if (!request.context) {
      return { valid: false, error: 'Event context is required' };
    }

    if (!request.context.eventName || !request.context.host) {
      return { valid: false, error: 'Event name and host are required' };
    }

    return { valid: true };
  }

  /**
   * Format error for response
   */
  private formatError(error: any): string {
    if (error instanceof Error) {
      return error.message;
    }
    return 'An unexpected error occurred';
  }

  /**
   * Save generation metadata to database
   */
  private async saveGenerationMetadata(metadata: GenerationMetadata): Promise<void> {
    try {
      await this.db.addData(DB_COLLECTIONS.AI_GENERATIONS, {
        ID: `${metadata.eventId}_${metadata.generationType}_${Date.now()}`,
        ...metadata,
      });
    } catch (error) {
      log('Failed to save generation metadata', { error, metadata });
    }
  }

  // Internal generation methods using specialized generators
  private async generateDigitalInviteInternal(request: GenAIGenerationRequest): Promise<DigitalInviteResult> {
    return await this.digitalInviteGenerator.generate(request);
  }

  private async generatePrintableInviteInternal(request: GenAIGenerationRequest): Promise<PrintableInviteResult> {
    return await this.printableInviteGenerator.generate(request);
  }

  private async generateEventDescriptionInternal(request: GenAIGenerationRequest): Promise<EventDescriptionResult> {
    return await this.eventDescriptionGenerator.generate(request);
  }
}

// Singleton instance
let genAIServiceInstance: GenAIService | null = null;

export function getGenAIService(): GenAIService {
  if (!genAIServiceInstance) {
    genAIServiceInstance = new GenAIService();
  }
  return genAIServiceInstance;
}
