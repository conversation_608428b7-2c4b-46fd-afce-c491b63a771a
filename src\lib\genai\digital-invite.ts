/**
 * Digital invite image generation using Gemini AI
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import { log } from '@/lib/logger';
import {
  GenAIGenerationRequest,
  DigitalInviteResult,
  GenAIConfig,
} from './types';
import {
  IMAGE_GENAI_CONFIG,
  IMAGE_DIMENSIONS,
  GENERATION_CONFIG,
} from './config';

export class DigitalInviteGenerator {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private config: GenAIConfig;

  constructor(config?: Partial<GenAIConfig>) {
    this.config = { ...IMAGE_GENAI_CONFIG, ...config };
    this.genAI = new GoogleGenerativeAI(this.config.apiKey);
    this.model = this.genAI.getGenerativeModel({
      model: this.config.model,
    });
  }

  /**
   * Generate digital invite image using Gemini AI
   */
  async generate(request: GenAIGenerationRequest): Promise<DigitalInviteResult> {
    try {
      // Determine dimensions based on options
      const dimensions = this.getDimensions(request.options);

      // Create image generation prompt
      const prompt = this.createImagePrompt(request.context, request.options, dimensions);

      log('Generating digital invite image with Gemini', {
        eventId: request.eventId,
        dimensions,
        theme: request.options?.theme,
      });

      // Generate image using Gemini
      const imageBuffer = await this.generateImageWithGemini(prompt);

      const result: DigitalInviteResult = {
        imageBuffer,
        dimensions,
        format: 'png',
        mimeType: 'image/png',
      };

      log('Digital invite image generated successfully', {
        eventId: request.eventId,
        imageSize: imageBuffer.length,
      });

      return result;

    } catch (error) {
      log('Digital invite generation failed', { error, request });
      throw new Error(`Failed to generate digital invite: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create image generation prompt for Gemini
   */
  private createImagePrompt(context: any, options: any, dimensions: { width: number; height: number }): string {
    const theme = options?.theme || 'modern';
    const colorScheme = options?.colorScheme || 'neutral';

    const eventDate = context.eventDate instanceof Date ? context.eventDate : new Date(context.eventDate);

    return `Create a beautiful digital invitation image for an event with the following details:

Event: ${context.eventName}
Host: ${context.host}
Date: ${eventDate.toLocaleDateString()}
Time: ${context.start} - ${context.end}
Location: ${context.location}
${context.message ? `Message: ${context.message}` : ''}

Design specifications:
- Style: ${theme} theme with ${colorScheme} color scheme
- Dimensions: ${dimensions.width}x${dimensions.height} pixels
- Format: Digital invitation optimized for web and mobile viewing
- Include all event details in an attractive, readable layout
- Use modern typography and appealing visual design
- Make it professional and eye-catching

Create a complete invitation image that people would be excited to receive.`;
  }

  /**
   * Generate image using Gemini AI
   */
  private async generateImageWithGemini(prompt: string): Promise<Buffer> {
    try {
      // Generate image with timeout
      const result = await Promise.race([
        this.model.generateContent(prompt),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Generation timeout')), GENERATION_CONFIG.timeout)
        ),
      ]);

      const response = await result.response;

      // For image generation, we need to handle the response differently
      // The response should contain image data
      if (!response) {
        throw new Error('Empty response from Gemini');
      }

      // Extract image data from response
      // Note: This is a placeholder - the actual implementation depends on Gemini's image generation API
      const imageData = response.candidates?.[0]?.content?.parts?.[0];

      if (!imageData) {
        throw new Error('No image data in response');
      }

      // Convert image data to buffer
      // This is a simplified implementation - actual implementation may vary
      const imageBuffer = Buffer.from(imageData.inlineData?.data || '', 'base64');

      if (imageBuffer.length === 0) {
        throw new Error('Empty image buffer');
      }

      return imageBuffer;

    } catch (error) {
      log('Gemini image generation failed', { error });
      throw new Error(`Gemini image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }



  /**
   * Determine image dimensions based on options
   */
  private getDimensions(options?: any): { width: number; height: number } {
    // If dimensions are explicitly provided, use them
    if (options?.dimensions) {
      return options.dimensions;
    }

    // Default to desktop dimensions for digital invites
    return IMAGE_DIMENSIONS.digitalInvite.desktop;
  }

}
