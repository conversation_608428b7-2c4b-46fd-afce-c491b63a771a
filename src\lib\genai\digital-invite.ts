/**
 * Digital invite generation using GenAI
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import sharp from 'sharp';
import { log } from '@/lib/logger';
import {
  GenAIGenerationRequest,
  DigitalInviteResult,
  GenAIConfig,
} from './types';
import {
  DIGITAL_INVITE_PROMPT,
  fillPromptTemplate,
} from './prompts';
import {
  DEFAULT_GENAI_CONFIG,
  IMAGE_DIMENSIONS,
  GENERATION_CONFIG,
} from './config';

export class DigitalInviteGenerator {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private config: GenAIConfig;

  constructor(config?: Partial<GenAIConfig>) {
    this.config = { ...DEFAULT_GENAI_CONFIG, ...config };
    this.genAI = new GoogleGenerativeAI(this.config.apiKey);
    this.model = this.genAI.getGenerativeModel({ 
      model: this.config.model,
      generationConfig: {
        maxOutputTokens: this.config.maxTokens,
        temperature: this.config.temperature,
        topP: this.config.topP,
        topK: this.config.topK,
      },
    });
  }

  /**
   * Generate digital invite SVG and convert to image
   */
  async generate(request: GenAIGenerationRequest): Promise<DigitalInviteResult> {
    try {
      // Determine dimensions based on options
      const dimensions = this.getDimensions(request.options);
      
      // Fill prompt template with event context and options
      const prompt = fillPromptTemplate(
        DIGITAL_INVITE_PROMPT,
        request.context,
        {
          ...request.options,
          dimensions,
        }
      );

      log('Generating digital invite with Gemini', {
        eventId: request.eventId,
        dimensions,
        theme: request.options?.theme,
      });

      // Generate SVG content using Gemini
      const svgContent = await this.generateSVGWithGemini(prompt);

      // Validate and clean SVG
      const cleanSvg = this.validateAndCleanSVG(svgContent, dimensions);

      // Convert SVG to PNG buffer
      const imageBuffer = await this.convertSVGToImage(cleanSvg, dimensions);

      const result: DigitalInviteResult = {
        svgContent: cleanSvg,
        imageBuffer,
        dimensions,
        format: 'png',
      };

      log('Digital invite generated successfully', {
        eventId: request.eventId,
        svgLength: cleanSvg.length,
        imageSize: imageBuffer.length,
      });

      return result;

    } catch (error) {
      log('Digital invite generation failed', { error, request });
      throw new Error(`Failed to generate digital invite: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate SVG content using Gemini AI
   */
  private async generateSVGWithGemini(prompt: { system: string; user: string }): Promise<string> {
    try {
      // Combine system and user prompts
      const fullPrompt = `${prompt.system}\n\n${prompt.user}`;

      // Generate content with timeout
      const result = await Promise.race([
        this.model.generateContent(fullPrompt),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Generation timeout')), GENERATION_CONFIG.timeout)
        ),
      ]);

      const response = await result.response;
      const text = response.text();

      if (!text) {
        throw new Error('Empty response from Gemini');
      }

      // Extract SVG content from response
      const svgMatch = text.match(/<svg[\s\S]*?<\/svg>/i);
      if (!svgMatch) {
        throw new Error('No valid SVG found in response');
      }

      return svgMatch[0];

    } catch (error) {
      log('Gemini SVG generation failed', { error });
      throw new Error(`Gemini generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate and clean SVG content
   */
  private validateAndCleanSVG(svgContent: string, dimensions: { width: number; height: number }): string {
    try {
      // Basic SVG validation and cleaning
      let cleanSvg = svgContent.trim();

      // Ensure SVG has proper dimensions
      if (!cleanSvg.includes('width=') || !cleanSvg.includes('height=')) {
        cleanSvg = cleanSvg.replace(
          /<svg([^>]*)>/i,
          `<svg$1 width="${dimensions.width}" height="${dimensions.height}">`
        );
      }

      // Ensure SVG has xmlns attribute
      if (!cleanSvg.includes('xmlns=')) {
        cleanSvg = cleanSvg.replace(
          /<svg([^>]*)>/i,
          '<svg$1 xmlns="http://www.w3.org/2000/svg">'
        );
      }

      // Remove any potentially dangerous content
      cleanSvg = cleanSvg.replace(/<script[\s\S]*?<\/script>/gi, '');
      cleanSvg = cleanSvg.replace(/on\w+="[^"]*"/gi, '');

      return cleanSvg;

    } catch (error) {
      log('SVG validation failed', { error });
      throw new Error(`SVG validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert SVG to PNG image buffer
   */
  private async convertSVGToImage(
    svgContent: string, 
    dimensions: { width: number; height: number }
  ): Promise<Buffer> {
    try {
      const imageBuffer = await sharp(Buffer.from(svgContent))
        .resize(dimensions.width, dimensions.height)
        .png({
          quality: 90,
          compressionLevel: 6,
        })
        .toBuffer();

      return imageBuffer;

    } catch (error) {
      log('SVG to image conversion failed', { error });
      throw new Error(`Image conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Determine image dimensions based on options
   */
  private getDimensions(options?: any): { width: number; height: number } {
    // If dimensions are explicitly provided, use them
    if (options?.dimensions) {
      return options.dimensions;
    }

    // Default to desktop dimensions for digital invites
    return IMAGE_DIMENSIONS.digitalInvite.desktop;
  }

  /**
   * Generate fallback SVG if AI generation fails
   */
  private generateFallbackSVG(
    request: GenAIGenerationRequest,
    dimensions: { width: number; height: number }
  ): string {
    const { context } = request;
    
    return `<svg width="${dimensions.width}" height="${dimensions.height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#4776E6;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#8E54E9;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#bg)" />
      <text x="50%" y="25%" font-family="Arial, sans-serif" font-size="36" font-weight="bold" text-anchor="middle" fill="white">You're Invited!</text>
      <text x="50%" y="35%" font-family="Arial, sans-serif" font-size="28" text-anchor="middle" fill="white">${context.eventName}</text>
      <text x="50%" y="45%" font-family="Arial, sans-serif" font-size="20" text-anchor="middle" fill="white">Hosted by ${context.host}</text>
      <text x="50%" y="55%" font-family="Arial, sans-serif" font-size="18" text-anchor="middle" fill="white">${context.eventDate.toLocaleDateString()}</text>
      <text x="50%" y="60%" font-family="Arial, sans-serif" font-size="18" text-anchor="middle" fill="white">${context.start} - ${context.end}</text>
      <text x="50%" y="70%" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="white">${context.location}</text>
    </svg>`;
  }
}
