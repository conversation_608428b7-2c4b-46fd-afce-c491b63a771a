import React, { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Check, ChevronsUpDown, FolderPlus, Loader2, Plus, Users } from "lucide-react";
import { Event, EventInviteListItem } from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";

interface MoveToGroupDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  event: Event;
  selectedInviteIds: string[];
  invites: EventInviteListItem[];
  onSuccess: () => void;
}

export default function MoveToGroupDialog({
  open,
  onOpenChange,
  event,
  selectedInviteIds,
  invites,
  onSuccess
}: MoveToGroupDialogProps) {
  const { toast } = useToast();
  const [isMoving, setIsMoving] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<string>("");
  const [isCreatingNew, setIsCreatingNew] = useState(false);
  const [newGroupName, setNewGroupName] = useState("");
  const [comboboxOpen, setComboboxOpen] = useState(false);
  const [allInvites, setAllInvites] = useState<EventInviteListItem[]>([]);

  // Fetch all invites for the event to get existing groups
  useEffect(() => {
    const fetchAllInvites = async () => {
      if (!event?.ID) return;
      
      try {
        const response = await fetch(`/api/event/${event.ID}/invites`);
        const data = await response.json();
        
        if (response.ok && data.invites) {
          setAllInvites(data.invites);
        }
      } catch (error) {
        console.error('Error fetching invites:', error);
      }
    };

    if (open) {
      fetchAllInvites();
    }
  }, [open, event?.ID]);

  // Extract unique groups from all invites
  const existingGroups = useMemo(() => {
    const groups = allInvites
      .map(invite => invite.group)
      .filter((group): group is string => 
        group !== undefined && 
        group !== null && 
        group.trim() !== '' &&
        group !== 'Ungrouped'
      );
    
    return [...new Set(groups)].sort();
  }, [allInvites]);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setSelectedGroup("");
      setIsCreatingNew(false);
      setNewGroupName("");
      setComboboxOpen(false);
    }
  }, [open]);

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleCreateNewGroup = () => {
    setIsCreatingNew(true);
    setComboboxOpen(false);
  };

  const handleConfirmNewGroup = () => {
    if (newGroupName.trim()) {
      setSelectedGroup(newGroupName.trim());
      setIsCreatingNew(false);
      setNewGroupName("");
    }
  };

  const handleCancelNewGroup = () => {
    setIsCreatingNew(false);
    setNewGroupName("");
  };

  const handleMoveToGroup = async () => {
    if (!selectedGroup && !isCreatingNew) {
      toast({
        title: "No group selected",
        description: "Please select a group or create a new one.",
        variant: "destructive"
      });
      return;
    }

    const finalGroupName = isCreatingNew ? newGroupName.trim() : selectedGroup;
    
    if (!finalGroupName) {
      toast({
        title: "Invalid group name",
        description: "Please enter a valid group name.",
        variant: "destructive"
      });
      return;
    }

    setIsMoving(true);
    try {
      const response = await fetch(`/api/event/${event.ID}/invites/bulk-update-group`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inviteIds: selectedInviteIds,
          groupName: finalGroupName
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to move invites to group');
      }

      const data = await response.json();

      toast({
        title: "Invites moved successfully!",
        description: `Moved ${data.updatedCount} invite${data.updatedCount !== 1 ? 's' : ''} to "${finalGroupName}".`,
      });

      onSuccess();
      onOpenChange(false);
    } catch (error) {
      console.error('Error moving invites to group:', error);
      toast({
        title: "Error moving invites",
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: "destructive"
      });
    } finally {
      setIsMoving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl">Move to Group</DialogTitle>
          <DialogDescription>
            Move selected invites to a group for better organization
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-5">
          {/* Selected invites info */}
          <div className="space-y-2">
            <h3 className="text-base font-medium">Selected Invites</h3>
            <div className="flex items-center gap-2">
              <div className="relative flex items-center justify-center w-9 h-9 rounded-full bg-[#F5F7FA]">
                <Users className="h-4 w-4 text-gray-500" />
              </div>
              <span className="text-sm">{selectedInviteIds.length} invite{selectedInviteIds.length !== 1 ? 's' : ''} selected</span>
            </div>
          </div>

          {/* Group selection */}
          <div className="space-y-2">
            <Label htmlFor="group-select">Select Group</Label>
            {!isCreatingNew ? (
              <Popover open={comboboxOpen} onOpenChange={setComboboxOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={comboboxOpen}
                    className="w-full justify-between"
                  >
                    {selectedGroup || "Select a group..."}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0" align="start">
                  <Command>
                    <CommandInput placeholder="Search groups..." />
                    <CommandList>
                      <CommandEmpty>No groups found.</CommandEmpty>
                      <CommandGroup>
                        {existingGroups.map((group) => (
                          <CommandItem
                            key={group}
                            value={group}
                            onSelect={(currentValue) => {
                              setSelectedGroup(currentValue === selectedGroup ? "" : currentValue);
                              setComboboxOpen(false);
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                selectedGroup === group ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {group}
                          </CommandItem>
                        ))}
                        <CommandItem onSelect={handleCreateNewGroup}>
                          <Plus className="mr-2 h-4 w-4" />
                          Create new group
                        </CommandItem>
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            ) : (
              <div className="space-y-2">
                <Input
                  value={newGroupName}
                  onChange={(e) => setNewGroupName(e.target.value)}
                  placeholder="Enter new group name"
                  className="w-full"
                />
                <div className="flex gap-2">
                  <Button
                    type="button"
                    size="sm"
                    onClick={handleConfirmNewGroup}
                    disabled={!newGroupName.trim()}
                  >
                    Create Group
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleCancelNewGroup}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="flex sm:justify-between gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="flex-1"
            disabled={isMoving}
          >
            Cancel
          </Button>
          <Button
            variant="primary-button"
            onClick={handleMoveToGroup}
            className="flex-1"
            disabled={isMoving || (!selectedGroup && !isCreatingNew) || (isCreatingNew && !newGroupName.trim())}
          >
            {isMoving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Moving...
              </>
            ) : (
              <>
                <FolderPlus className="h-4 w-4 mr-2" />
                Move to Group
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
