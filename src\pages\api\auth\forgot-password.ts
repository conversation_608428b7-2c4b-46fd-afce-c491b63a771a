import { NextApiRequest, NextApiResponse } from 'next';
import { Database } from '@/lib/database';
import { z } from 'zod';
import { sendPasswordResetEmail } from '@/lib/mailer';
import { debugLog } from '@/lib/logger';

// Define validation schema for forgot password request
const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Validate request body
    const validationResult = forgotPasswordSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: validationResult.error.errors 
      });
    }

    const { email } = validationResult.data;

    // Get database instance
    const db = Database.getInstance();

    // Generate a password reset token
    const result = await db.generatePasswordResetToken(email);
    
    // Always return success, even if the email doesn't exist (for security)
    if (!result) {
      debugLog(`Password reset requested for non-existent email: ${email}`);
      return res.status(200).json({ 
        success: true, 
        message: 'If your email exists in our system, you will receive a password reset link shortly.' 
      });
    }

    // Construct the reset URL
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const resetUrl = `${baseUrl}/auth/reset-password?token=${result.token}`;

    // Send the password reset email
    const emailSent = await sendPasswordResetEmail(email, resetUrl);

    if (!emailSent) {
      return res.status(500).json({ error: 'Failed to send password reset email' });
    }

    debugLog(`Password reset email sent to ${email}`);

    // Return success response
    return res.status(200).json({ 
      success: true, 
      message: 'If your email exists in our system, you will receive a password reset link shortly.' 
    });
  } catch (error) {
    console.error('Error in forgot password handler:', error);
    return res.status(500).json({ error: 'Failed to process password reset request' });
  }
}
