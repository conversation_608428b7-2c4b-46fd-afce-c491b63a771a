'use client'

import { Head<PERSON> } from "@/components/Header"
import { Footer } from "@/components/Footer"
import { Separator } from "@/components/ui/separator"
import Image from "next/image"
import { useSession } from "next-auth/react"
import { useRouter } from "next/router"

export default function AboutUsPage() {
  const router = useRouter()
  const { data: session } = useSession()

  return (
    <div className="flex flex-col min-h-screen">
      <Header
        buttons={session ? [
          {
            label: "Manage Events",
            onClick: () => router.push('/events'),
            variant: "outline"
          }
        ] : []}
      />

      <main className="flex-1 container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">About Us</h1>
        <Separator className="my-4" />
        
        <div className="prose max-w-none">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div>
              <h2 className="text-2xl font-semibold mb-4">Our Mission</h2>
              <p className="text-gray-700 mb-4">
                At I am Coming (IaC), our mission is to simplify event management by providing intuitive RSVP 
                solutions that connect event hosts with their guests seamlessly. We believe that planning 
                events should be joyful, not stressful.
              </p>
              <p className="text-gray-700">
                We&apos;re dedicated to creating technology that enhances human connections, making it easier 
                for people to come together and celebrate life&apos;s important moments.
              </p>
            </div>
            <div className="flex items-center justify-center">
              <Image 
                src="/iac-logo.svg" 
                alt="I am Coming Logo" 
                width={300} 
                height={200}
                className="h-auto"
              />
            </div>
          </div>

          <h2 className="text-2xl font-semibold mt-10 mb-6">Our Story</h2>
          <p className="text-gray-700 mb-4">
            I am Coming was born out of a real need. As a parent organizing events for my children, 
            I constantly struggled with the chaotic process of sending invites to classmates&apos; parents 
            and tracking who was attending and how many people were coming. The back-and-forth communication 
            and manual tracking became overwhelming.
          </p>
          <p className="text-gray-700 mb-4">
            Initially, I created a proof of concept using Google Forms and Google Sheets to solve this problem, 
            which proved effective but had limitations. This experience inspired me to build a dedicated platform 
            specifically designed to streamline the invitation and RSVP management workflow.
          </p>
          <p className="text-gray-700 mb-8">
            The first version of I am Coming (IaC) was developed in March 2025 and is currently in beta mode. 
            Our platform continues to evolve based on user feedback and our commitment to making event 
            organization simpler for everyone.
          </p>

          <div className="bg-gray-50 p-6 rounded-lg mb-12">
            <h2 className="text-2xl font-semibold mb-4">Our Values</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h3 className="text-xl font-medium mb-2">Simplicity</h3>
                <p className="text-gray-700">
                  We believe in straightforward solutions that don&apos;t require technical expertise to use.
                </p>
              </div>
              <div>
                <h3 className="text-xl font-medium mb-2">Connection</h3>
                <p className="text-gray-700">
                  We build tools that strengthen relationships and create meaningful experiences.
                </p>
              </div>
              <div>
                <h3 className="text-xl font-medium mb-2">Privacy</h3>
                <p className="text-gray-700">
                  We respect your data and the data of your guests, ensuring it&apos;s always secure.
                </p>
              </div>
            </div>
          </div>

          <h2 className="text-2xl font-semibold mb-6">Our Founder</h2>
          <div className="flex flex-col md:flex-row items-center md:items-start gap-8 mb-12 bg-white p-6 rounded-lg shadow-sm">
            <div className="w-32 h-32 rounded-full overflow-hidden flex-shrink-0">
              <Image 
                src="/balwant.jpeg" 
                alt="Balwant Singh" 
                width={128} 
                height={128}
                className="object-cover w-full h-full"
              />
            </div>
            <div>
              <h3 className="text-xl font-medium">Balwant Singh</h3>
              <p className="text-gray-600 mb-3">Founder</p>
              <p className="text-gray-700 mb-4">
                Balwant founded I am Coming after experiencing firsthand the challenges of managing RSVPs for children&apos;s events.
                With a background in technology and a passion for solving real-world problems, he developed IaC to make event 
                organization accessible and stress-free for everyone.
              </p>
              <div className="flex flex-wrap gap-4">
                <a 
                  href="https://www.linkedin.com/in/balwantmatharu/" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-primary hover:text-primary/80 transition-colors"
                >
                  <Image 
                    src="/linkedin.png" 
                    alt="LinkedIn" 
                    width={20} 
                    height={20} 
                    className="h-5 w-5 mr-2" 
                  />
                  Connect with Founder
                </a>
                <a 
                  href="https://www.linkedin.com/company/iamcoming/" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-primary hover:text-primary/80 transition-colors"
                >
                  <Image 
                    src="/linkedin.png" 
                    alt="LinkedIn" 
                    width={20} 
                    height={20} 
                    className="h-5 w-5 mr-2" 
                  />
                  Follow IaC on LinkedIn
                </a>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer type="marketing" />
    </div>
  )
}