/**
 * Printable invite generation using GenAI
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import sharp from 'sharp';
import { log } from '@/lib/logger';
import {
  GenAIGenerationRequest,
  PrintableInviteResult,
  GenAIConfig,
} from './types';
import {
  PRINTABLE_INVITE_PROMPT,
  fillPromptTemplate,
} from './prompts';
import {
  DEFAULT_GENAI_CONFIG,
  IMAGE_DIMENSIONS,
  GENERATION_CONFIG,
} from './config';

export class PrintableInviteGenerator {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private config: GenAIConfig;

  constructor(config?: Partial<GenAIConfig>) {
    this.config = { ...DEFAULT_GENAI_CONFIG, ...config };
    this.genAI = new GoogleGenerativeAI(this.config.apiKey);
    this.model = this.genAI.getGenerativeModel({ 
      model: this.config.model,
      generationConfig: {
        maxOutputTokens: this.config.maxTokens,
        temperature: this.config.temperature,
        topP: this.config.topP,
        topK: this.config.topK,
      },
    });
  }

  /**
   * Generate printable invite SVG and convert to high-res image
   */
  async generate(request: GenAIGenerationRequest): Promise<PrintableInviteResult> {
    try {
      // Determine dimensions and paper settings
      const paperSize = request.options?.paperSize || 'A5';
      const orientation = request.options?.orientation || 'portrait';
      const dimensions = this.getDimensions(paperSize, orientation);
      
      // Fill prompt template with event context and options
      const prompt = fillPromptTemplate(
        PRINTABLE_INVITE_PROMPT,
        request.context,
        {
          ...request.options,
          paperSize,
          orientation,
          dimensions,
        }
      );

      log('Generating printable invite with Gemini', {
        eventId: request.eventId,
        paperSize,
        orientation,
        dimensions,
        theme: request.options?.theme,
      });

      // Generate SVG content using Gemini
      const svgContent = await this.generateSVGWithGemini(prompt);

      // Validate and clean SVG for printing
      const cleanSvg = this.validateAndCleanSVG(svgContent, dimensions);

      // Convert SVG to high-resolution PNG buffer (300 DPI)
      const imageBuffer = await this.convertSVGToHighResImage(cleanSvg, dimensions);

      const result: PrintableInviteResult = {
        svgContent: cleanSvg,
        imageBuffer,
        dimensions,
        paperSize,
        orientation,
        format: 'png',
      };

      log('Printable invite generated successfully', {
        eventId: request.eventId,
        paperSize,
        orientation,
        svgLength: cleanSvg.length,
        imageSize: imageBuffer.length,
      });

      return result;

    } catch (error) {
      log('Printable invite generation failed', { error, request });
      throw new Error(`Failed to generate printable invite: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate SVG content using Gemini AI
   */
  private async generateSVGWithGemini(prompt: { system: string; user: string }): Promise<string> {
    try {
      // Combine system and user prompts
      const fullPrompt = `${prompt.system}\n\n${prompt.user}`;

      // Generate content with timeout
      const result = await Promise.race([
        this.model.generateContent(fullPrompt),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Generation timeout')), GENERATION_CONFIG.timeout)
        ),
      ]);

      const response = await result.response;
      const text = response.text();

      if (!text) {
        throw new Error('Empty response from Gemini');
      }

      // Extract SVG content from response
      const svgMatch = text.match(/<svg[\s\S]*?<\/svg>/i);
      if (!svgMatch) {
        throw new Error('No valid SVG found in response');
      }

      return svgMatch[0];

    } catch (error) {
      log('Gemini SVG generation failed', { error });
      throw new Error(`Gemini generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate and clean SVG content for printing
   */
  private validateAndCleanSVG(svgContent: string, dimensions: { width: number; height: number }): string {
    try {
      // Basic SVG validation and cleaning
      let cleanSvg = svgContent.trim();

      // Ensure SVG has proper dimensions
      if (!cleanSvg.includes('width=') || !cleanSvg.includes('height=')) {
        cleanSvg = cleanSvg.replace(
          /<svg([^>]*)>/i,
          `<svg$1 width="${dimensions.width}" height="${dimensions.height}">`
        );
      }

      // Ensure SVG has xmlns attribute
      if (!cleanSvg.includes('xmlns=')) {
        cleanSvg = cleanSvg.replace(
          /<svg([^>]*)>/i,
          '<svg$1 xmlns="http://www.w3.org/2000/svg">'
        );
      }

      // Remove any potentially dangerous content
      cleanSvg = cleanSvg.replace(/<script[\s\S]*?<\/script>/gi, '');
      cleanSvg = cleanSvg.replace(/on\w+="[^"]*"/gi, '');

      // Optimize for printing - ensure high contrast colors
      cleanSvg = this.optimizeForPrinting(cleanSvg);

      return cleanSvg;

    } catch (error) {
      log('SVG validation failed', { error });
      throw new Error(`SVG validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Optimize SVG for printing (high contrast, print-safe colors)
   */
  private optimizeForPrinting(svgContent: string): string {
    // Replace very light colors with darker alternatives for better printing
    let optimized = svgContent;
    
    // Replace light grays with darker grays
    optimized = optimized.replace(/fill="#f[0-9a-f]{5}"/gi, 'fill="#666666"');
    optimized = optimized.replace(/stroke="#f[0-9a-f]{5}"/gi, 'stroke="#333333"');
    
    // Ensure minimum stroke width for printing
    optimized = optimized.replace(/stroke-width="0\.?[1-9]"/gi, 'stroke-width="1"');
    
    return optimized;
  }

  /**
   * Convert SVG to high-resolution PNG image buffer (300 DPI)
   */
  private async convertSVGToHighResImage(
    svgContent: string, 
    dimensions: { width: number; height: number }
  ): Promise<Buffer> {
    try {
      const imageBuffer = await sharp(Buffer.from(svgContent))
        .resize(dimensions.width, dimensions.height)
        .png({
          quality: 100, // Maximum quality for printing
          compressionLevel: 1, // Minimal compression
        })
        .toBuffer();

      return imageBuffer;

    } catch (error) {
      log('SVG to image conversion failed', { error });
      throw new Error(`Image conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Determine image dimensions based on paper size and orientation
   */
  private getDimensions(paperSize: string, orientation: string): { width: number; height: number } {
    const paperDimensions = IMAGE_DIMENSIONS.printableInvite[paperSize as keyof typeof IMAGE_DIMENSIONS.printableInvite];
    
    if (!paperDimensions) {
      // Default to A5 if paper size not found
      return IMAGE_DIMENSIONS.printableInvite.A5.portrait;
    }

    return paperDimensions[orientation as keyof typeof paperDimensions] || paperDimensions.portrait;
  }

  /**
   * Generate fallback SVG if AI generation fails
   */
  private generateFallbackSVG(
    request: GenAIGenerationRequest,
    dimensions: { width: number; height: number }
  ): string {
    const { context } = request;
    
    return `<svg width="${dimensions.width}" height="${dimensions.height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#ffffff" stroke="#cccccc" stroke-width="2"/>
      <rect x="100" y="100" width="${dimensions.width - 200}" height="${dimensions.height - 200}" fill="none" stroke="#333333" stroke-width="1"/>
      <text x="50%" y="20%" font-family="serif" font-size="48" font-weight="bold" text-anchor="middle" fill="#333333">You're Invited</text>
      <line x1="20%" y1="25%" x2="80%" y2="25%" stroke="#666666" stroke-width="2"/>
      <text x="50%" y="35%" font-family="serif" font-size="32" text-anchor="middle" fill="#333333">${context.eventName}</text>
      <text x="50%" y="45%" font-family="serif" font-size="24" text-anchor="middle" fill="#666666">Hosted by ${context.host}</text>
      <text x="50%" y="55%" font-family="serif" font-size="20" text-anchor="middle" fill="#333333">${context.eventDate.toLocaleDateString()}</text>
      <text x="50%" y="60%" font-family="serif" font-size="20" text-anchor="middle" fill="#333333">${context.start} - ${context.end}</text>
      <text x="50%" y="70%" font-family="serif" font-size="18" text-anchor="middle" fill="#666666">${context.location}</text>
    </svg>`;
  }
}
