/**
 * Printable invite image generation using Gemini AI
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import { log } from '@/lib/logger';
import {
  GenAIGenerationRequest,
  PrintableInviteResult,
  GenAIConfig,
} from './types';
import {
  IMAGE_GENAI_CONFIG,
  IMAGE_DIMENSIONS,
  GENERATION_CONFIG,
} from './config';

export class PrintableInviteGenerator {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private config: GenAIConfig;

  constructor(config?: Partial<GenAIConfig>) {
    this.config = { ...IMAGE_GENAI_CONFIG, ...config };
    this.genAI = new GoogleGenerativeAI(this.config.apiKey);
    this.model = this.genAI.getGenerativeModel({
      model: this.config.model,
    });
  }

  /**
   * Generate printable invite image using Gemini AI
   */
  async generate(request: GenAIGenerationRequest): Promise<PrintableInviteResult> {
    try {
      // Determine dimensions and paper settings
      const paperSize = request.options?.paperSize || 'A5';
      const orientation = request.options?.orientation || 'portrait';
      const dimensions = this.getDimensions(paperSize, orientation);

      // Create image generation prompt
      const prompt = this.createImagePrompt(request.context, request.options, dimensions, paperSize, orientation);

      log('Generating printable invite image with Gemini', {
        eventId: request.eventId,
        paperSize,
        orientation,
        dimensions,
        theme: request.options?.theme,
      });

      // Generate image using Gemini
      const imageBuffer = await this.generateImageWithGemini(prompt);

      const result: PrintableInviteResult = {
        imageBuffer,
        dimensions,
        paperSize,
        orientation,
        format: 'png',
        mimeType: 'image/png',
      };

      log('Printable invite image generated successfully', {
        eventId: request.eventId,
        paperSize,
        orientation,
        imageSize: imageBuffer.length,
      });

      return result;

    } catch (error) {
      log('Printable invite generation failed', { error, request });
      throw new Error(`Failed to generate printable invite: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create image generation prompt for printable invites
   */
  private createImagePrompt(
    context: any,
    options: any,
    dimensions: { width: number; height: number },
    paperSize: string,
    orientation: string
  ): string {
    const theme = options?.theme || 'elegant';
    const colorScheme = options?.colorScheme || 'neutral';

    const eventDate = context.eventDate instanceof Date ? context.eventDate : new Date(context.eventDate);

    return `Create a high-quality printable invitation image for an event with the following details:

Event: ${context.eventName}
Host: ${context.host}
Date: ${eventDate.toLocaleDateString()}
Time: ${context.start} - ${context.end}
Location: ${context.location}
${context.message ? `Message: ${context.message}` : ''}

Print specifications:
- Paper size: ${paperSize}
- Orientation: ${orientation}
- Dimensions: ${dimensions.width}x${dimensions.height} pixels (300 DPI for high-quality printing)
- Style: ${theme} theme with ${colorScheme} color scheme
- Format: High-resolution image optimized for printing

Design requirements:
- Professional print-ready design with high contrast colors
- Clear, readable typography suitable for printing
- Proper margins and spacing for the specified paper size
- Elegant layout that works well when printed
- Include all event details in an attractive, organized format
- Use print-safe fonts and colors that will look good on paper

Create a beautiful invitation that people would be proud to print and share.`;
  }

  /**
   * Generate image using Gemini AI
   */
  private async generateImageWithGemini(prompt: string): Promise<Buffer> {
    try {
      // Generate image with timeout
      const result = await Promise.race([
        this.model.generateContent(prompt),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Generation timeout')), GENERATION_CONFIG.timeout)
        ),
      ]);

      const response = await result.response;

      // For image generation, we need to handle the response differently
      if (!response) {
        throw new Error('Empty response from Gemini');
      }

      // Extract image data from response
      const imageData = response.candidates?.[0]?.content?.parts?.[0];

      if (!imageData) {
        throw new Error('No image data in response');
      }

      // Convert image data to buffer
      const imageBuffer = Buffer.from(imageData.inlineData?.data || '', 'base64');

      if (imageBuffer.length === 0) {
        throw new Error('Empty image buffer');
      }

      return imageBuffer;

    } catch (error) {
      log('Gemini image generation failed', { error });
      throw new Error(`Gemini image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }



  /**
   * Determine image dimensions based on paper size and orientation
   */
  private getDimensions(paperSize: string, orientation: string): { width: number; height: number } {
    const paperDimensions = IMAGE_DIMENSIONS.printableInvite[paperSize as keyof typeof IMAGE_DIMENSIONS.printableInvite];
    
    if (!paperDimensions) {
      // Default to A5 if paper size not found
      return IMAGE_DIMENSIONS.printableInvite.A5.portrait;
    }

    return paperDimensions[orientation as keyof typeof paperDimensions] || paperDimensions.portrait;
  }

}
