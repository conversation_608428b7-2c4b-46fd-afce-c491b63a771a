import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if user is authenticated
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { eventId } = req.query;

    if (!eventId) {
      return res.status(400).json({ error: 'Event ID is required' });
    }

    // Ensure eventId is a string (not an array)
    const eventIdString = Array.isArray(eventId) ? eventId[0] : eventId;

    const db = Database.getInstance();

    // Get the event
    const event = await db.readData('events', eventIdString);
    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    // Check if user has access to this event
    const organization = await db.getOrganizationById(event.ownerAccountId);
    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const isMember = organization.members?.some(member => member.userId === session.user.id);
    const isOwner = event.ownerAccountId === session.user.id;
    const isManager = event.managers?.includes(session.user.email);

    if (!isMember && !isOwner && !isManager) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get all invites for this event
    const invitesSnapshot = await db.query('invites')
      .where('eventId', '==', eventIdString)
      .get();

    const invites = invitesSnapshot.docs.map(doc => doc.data());

    // Group invites by their group field
    const groupMap = new Map<string, any[]>();
    
    invites.forEach(invite => {
      const groupName = invite.group || 'Ungrouped';
      if (!groupMap.has(groupName)) {
        groupMap.set(groupName, []);
      }
      groupMap.get(groupName)!.push(invite);
    });

    // Convert to contact groups format
    const contactGroups: any[] = [];
    
    groupMap.forEach((groupInvites, groupName) => {
      const contacts = groupInvites
        .filter(invite => invite.email || invite.phone)
        .map(invite => ({
          email: invite.email || '',
          name: invite.name,
          phone: invite.phone || ''
        }))
        .filter(contact => contact.email || contact.phone);

      contactGroups.push({
        name: groupName,
        contactCount: contacts.length,
        contacts: contacts,
        hasValidContacts: contacts.length > 0
      });
    });

    // Check if event is past due (24 hours after event date)
    const eventDate = new Date(event.eventDate);
    const twentyFourHoursAgo = new Date();
    twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);
    const isPastDue = eventDate < twentyFourHoursAgo;

    // Check organization settings (simplified for one-time email)
    const shouldSendEmail = !organization.contactGroupSettings?.hasBeenAsked;

    return res.status(200).json({
      success: true,
      eventId: eventIdString,
      eventName: event.eventName,
      eventDate: event.eventDate,
      organizationId: organization.id,
      organizationName: organization.name,
      ownerEmail: event.ownerEmail,
      status: event.status || 'active',
      isPastDue,
      totalInvites: invites.length,
      contactGroups,
      savableGroups: contactGroups.filter(group => group.name !== 'Ungrouped' && group.hasValidContacts),
      canTriggerEmail: {
        shouldSendEmail,
        hasValidGroups: contactGroups.some(group => group.name !== 'Ungrouped' && group.hasValidContacts),
        organizationSettings: organization.contactGroupSettings || null
      }
    });

  } catch (error) {
    console.error('Error checking event groups:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
