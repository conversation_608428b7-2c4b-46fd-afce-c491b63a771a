import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';
import { extractContactGroupsFromEvent } from '@/lib/saved-contact-groups';

import { ContactGroupData } from '@/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Users, Mail, Phone, CheckCircle, Info } from 'lucide-react';

import { useToast } from '@/hooks/use-toast';
import { ProtectedLayout } from '@/components/layouts/ProtectedLayout';
import { Header } from '@/components/Header';

interface SaveContactGroupsPageProps {
  organization: any;
  event: any;
  contactGroups: ContactGroupData[];
}

export default function SaveContactGroupsPage({
  organization,
  event,
  contactGroups
}: SaveContactGroupsPageProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Initialize selected groups (all selected by default)
    const allGroupNames = contactGroups.map(group => group.name);
    setSelectedGroups(allGroupNames);
  }, [contactGroups]);

  const handleGroupToggle = (groupName: string) => {
    setSelectedGroups(prev =>
      prev.includes(groupName)
        ? prev.filter(name => name !== groupName)
        : [...prev, groupName]
    );
  };

  const handleSave = async () => {
    if (selectedGroups.length === 0) {
      toast({
        title: "No groups selected",
        description: "Please select at least one group to save.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/organizations/${organization.id}/save-contact-groups`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          eventId: event.ID,
          groupsToSave: selectedGroups,
          preference: 'save'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save contact groups');
      }

      const data = await response.json();

      toast({
        title: "Success!",
        description: `Successfully saved ${data.savedGroups} contact group(s).`
      });

      // Redirect to organization dashboard or contact groups page
      router.push(`/organizations/${organization.id}/contact-groups`);

    } catch (error) {
      console.error('Error saving contact groups:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save contact groups",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };



  if (!organization) {
    return (
      <ProtectedLayout>
        <div className="flex flex-col bg-gray-50">
          <Header title="Save Contact Groups" />
          <div className="flex-1 p-4 pb-20">
            <div className="container mx-auto">
              <Card>
                <CardHeader>
                  <CardTitle>Event Not Found</CardTitle>
                  <CardDescription>
                    The event you&apos;re looking for could not be found.
                  </CardDescription>
                </CardHeader>
              </Card>
            </div>
          </div>
        </div>
      </ProtectedLayout>
    );
  }

  return (
    <ProtectedLayout>
      <div className="flex flex-col bg-gray-50">
        <Header
          title="Save Contact Groups"
          breadcrumbs={[
            { label: organization.name, href: '#' }
          ]}
        />

        {/* Main Content */}
        <div className="flex-1 p-4 pb-20">
          <div className="container mx-auto">
            <Card>
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start">
                  <div className="mb-3 sm:mb-0">
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      Save Contact Groups for Future Events
                    </CardTitle>
                    <CardDescription className="mt-2">
                      Your event &quot;{event.eventName}&quot; has been completed. Save your contact groups to reuse them in future events.
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Event Info */}
                <div className="mb-6 px-6 py-4 border-t -mx-6" style={{backgroundColor: '#F9FAFB', borderColor: '#E2E8F0'}}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg text-gray-900">{event.eventName}</h3>
                        <p className="text-sm text-gray-500">Event completed • {organization.name}</p>
                      </div>
                    </div>
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      {contactGroups.length} groups found
                    </Badge>
                  </div>
                </div>

                {/* Contact Groups */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-4 text-gray-900">Select Contact Groups to Save</h3>

                  {contactGroups.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-12 px-6 text-center border border-gray-200 rounded-lg bg-white">
                      <Users className="h-12 w-12 text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Contact Groups Found</h3>
                      <p className="text-gray-500 text-sm">
                        This event doesn&apos;t have any organized contact groups to save.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {contactGroups.map((group) => (
                        <div key={group.name} className="border border-gray-200 rounded-lg p-4 bg-white">
                          <div className="flex items-start space-x-3">
                            <Checkbox
                              id={`group-${group.name}`}
                              checked={selectedGroups.includes(group.name)}
                              onCheckedChange={() => handleGroupToggle(group.name)}
                              className="mt-1"
                            />
                            <div className="flex-1">
                              <div className="flex items-center justify-between mb-3">
                                <Label htmlFor={`group-${group.name}`} className="text-base font-medium text-gray-900 cursor-pointer">
                                  {group.name}
                                </Label>
                                <Badge variant="secondary">
                                  {group.contacts.length} contacts
                                </Badge>
                              </div>
                              {/* Simple contacts list */}
                              <div className="text-sm text-gray-600 mb-2">
                                Contacts in this group:
                              </div>
                              <div className="bg-gray-50 rounded-md p-3 max-h-24 overflow-y-auto">
                                {group.contacts.length === 0 ? (
                                  <p className="text-sm text-gray-500 italic">No contacts in this group</p>
                                ) : (
                                  <div className="space-y-1">
                                    {group.contacts.map((contact, contactIndex) => (
                                      <div key={contactIndex} className="text-sm">
                                        <span className="font-medium text-gray-900">
                                          {contact.name || contact.email || contact.phone || 'Unknown'}
                                        </span>
                                        {contact.name && (contact.email || contact.phone) && (
                                          <span className="text-gray-500 ml-2">
                                            ({contact.email || contact.phone})
                                          </span>
                                        )}
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>

                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Information about future management */}
                <div className="mb-6 p-4 rounded-lg bg-blue-50 border border-blue-200">
                  <h3 className="text-base font-semibold mb-2 text-blue-900">What happens next?</h3>
                  <p className="text-sm text-blue-800 mb-3">
                    After saving these groups, you can manage them and use them in future events through your profile menu.
                  </p>
                  <p className="text-xs text-blue-700 font-medium">
                    💡 This is a one-time email. All future management happens on the website. You can close this page if you don&apos;t want to save any groups.
                  </p>
                </div>

                {/* Action Button */}
                <div className="flex justify-center pt-4">
                  <Button
                    onClick={handleSave}
                    disabled={isSubmitting || selectedGroups.length === 0}
                    className="bg-[#F43F5E] hover:bg-[#F43F5E]/90 px-8"
                  >
                    {isSubmitting ? 'Saving...' : `Save ${selectedGroups.length} Group${selectedGroups.length !== 1 ? 's' : ''}`}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  try {
    const { organizationId, eventId } = context.query;

    if (!organizationId || !eventId) {
      return {
        notFound: true
      };
    }

    // Check user session
    const session = await getServerSession(context.req, context.res, authConfig);
    if (!session?.user?.id) {
      return {
        redirect: {
          destination: '/auth/signin',
          permanent: false
        }
      };
    }

    const db = Database.getInstance();

    // Check organization membership
    const organization = await db.getOrganizationById(organizationId as string);
    if (!organization) {
      return {
        notFound: true
      };
    }

    const isMember = organization.members?.some(member => member.userId === session.user.id);
    if (!isMember) {
      return {
        notFound: true
      };
    }

    // Get event details
    const event = await db.readData('events', eventId as string);
    if (!event || event.ownerAccountId !== organizationId) {
      return {
        notFound: true
      };
    }

    // Check if user owns the event or is a manager
    const isOwner = (event.ownerEmail === session.user.email) ||
                   (event.ownerAccountId === session.user.id);
    const isManager = event.managers && event.managers.some((manager: any) =>
      manager.email === session.user.email || manager.accountId === session.user.id
    );

    if (!isOwner && !isManager) {
      return {
        notFound: true
      };
    }

    // Get contact groups from the event
    const contactGroups = await extractContactGroupsFromEvent(eventId as string);

    return {
      props: {
        organization,
        event,
        contactGroups
      }
    };

  } catch (error) {
    console.error('Error in getServerSideProps:', error);
    return {
      notFound: true
    };
  }
};
