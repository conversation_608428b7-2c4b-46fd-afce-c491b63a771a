/**
 * API endpoint for generating event descriptions using GenAI
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { getGenAIService } from '@/lib/genai';
import { Database } from '@/lib/database';
import { log } from '@/lib/logger';
import { rateLimit } from '@/lib/rateLimiter';
import { GenAIGenerationRequest, EventContext } from '@/lib/genai/types';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Apply basic rate limiting (20 requests per minute per IP - higher for text generation)
  if (!rateLimit(req, res, 20, 60 * 1000)) {
    return res.status(429).json({
      error: 'Too many requests, please try again later',
      retryAfter: res.getHeader('Retry-After')
    });
  }

  try {
    // Check authentication
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Extract request data
    const { eventId, options = {} } = req.body;

    if (!eventId) {
      return res.status(400).json({ error: 'Event ID is required' });
    }

    // Get event data from database
    const db = Database.getInstance();
    const event = await db.readData('events', eventId);

    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    // Check if user has permission to generate for this event
    const isOwner = event.ownerAccountId === session.user.id;
    const isManager = event.managers && event.managers.includes(session.user.id);

    if (!isOwner && !isManager) {
      return res.status(403).json({ error: 'Permission denied' });
    }

    // Prepare event context
    const eventContext: EventContext = {
      eventName: event.eventName,
      eventDate: new Date(event.eventDate),
      start: event.start,
      end: event.end,
      location: event.location,
      timezone: event.timezone,
      message: event.message,
      host: event.host,
      rsvpDueDate: event.rsvpDueDate ? new Date(event.rsvpDueDate) : undefined,
    };

    // Prepare generation request
    const generationRequest: GenAIGenerationRequest = {
      eventId,
      userId: session.user.id,
      type: 'event-description',
      context: eventContext,
      options: {
        tone: options.tone || 'friendly',
        length: options.length || 'medium',
        includeDetails: options.includeDetails !== false, // Default to true
        ...options,
      },
    };

    // Generate event description
    const genAIService = getGenAIService();
    const result = await genAIService.generateEventDescription(generationRequest);

    if (!result.success) {
      log('Event description generation failed', { 
        eventId, 
        userId: session.user.id, 
        error: result.error 
      });
      
      return res.status(400).json({ 
        error: result.error || 'Failed to generate event description' 
      });
    }

    log('Event description generated successfully', { 
      eventId, 
      userId: session.user.id,
      usage: result.usage 
    });

    return res.status(200).json({
      success: true,
      data: result.data,
      usage: result.usage,
    });

  } catch (error) {
    console.error('Event description generation error:', error);
    log('Event description generation error', { error });
    
    return res.status(500).json({ 
      error: 'Internal server error' 
    });
  }
}

// Configure API route
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
};
