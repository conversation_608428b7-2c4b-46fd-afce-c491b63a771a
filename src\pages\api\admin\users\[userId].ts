import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { Database } from '@/lib/database';
import { authConfig } from '@/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check if the user is authenticated and is an admin
    const session = await getServerSession(req, res, authConfig);

    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if the current user has admin permissions
    if (!session.user?.isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    // Get user ID from the query parameters
    const { userId } = req.query;

    if (!userId || Array.isArray(userId)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    // Handle GET request - fetch user by ID
    if (req.method === 'GET') {
      const db = Database.getInstance();
      const user = await db.getUserById(userId);

      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Return user data
      return res.status(200).json({
        success: true,
        user,
      });
    }

    // If not GET, return method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error handling admin user request:', error);
    return res.status(500).json({ error: 'Failed to process admin user request' });
  }
}