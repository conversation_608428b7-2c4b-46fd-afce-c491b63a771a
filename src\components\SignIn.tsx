"use client"

import { signIn, useSession } from "next-auth/react"
import { Loader2, CheckCircle2 } from "lucide-react"
import { Alert, AlertDescription } from "./ui/alert"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { useState, useEffect } from "react"
// We're using signIn directly now
import { useGoogleAuth } from "@/hooks/useGoogleAuth"
import { useRouter } from "next/router"
import { Separator } from "./ui/separator"
import Image from "next/image"
import { PasswordSignIn } from "./PasswordSignIn";
import { EmailMagicLinkSignIn } from "./EmailMagicLinkSignIn";
import { GoogleSignInButton } from "./GoogleSignInButton";

export function SignInForm() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'email' | 'google' | 'password'>('password');
  const [showSignUp, setShowSignUp] = useState(false);

  // Check if there's a provider parameter in the URL
  useEffect(() => {
    const provider = router.query.provider;
    if (provider === 'email') {
      setActiveTab('email');
    }
  }, [router.query]);

  // Don't redirect here - let the parent SignInPage handle it
  // This prevents duplicate redirects
  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      // Log organization details
      console.log("User authenticated:", session.user.id);
      console.log("User name:", session.user.name);
      console.log("User email:", session.user.email);

      // Log organization details if available
      if (session.user.organization) {
        console.log("Organization details:");
        console.log("  ID:", session.user.organization.id);
        console.log("  Name:", session.user.organization.name);
        console.log("  Type:", session.user.organization.type);
      } else {
        console.log("No organization details available");
      }

      // Redirection is handled by the parent SignInPage component
    }
  }, [status, session]);

  // Show loading state when checking authentication status
  if (status === 'loading') {
    return (
      <div className="container flex min-h-screen w-full flex-col items-center justify-center py-12">
        <div className="flex flex-col items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="mt-2 text-sm text-muted-foreground">Checking your authentication status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container flex min-h-screen w-full flex-col items-center justify-center py-12">
      <div className="mb-8">
        <Image
          src="/iac-logo-large.svg"
          alt="I am Coming - Event RSVP Management Platform"
          className="h-10 w-auto cursor-pointer"
          width={200}
          height={40}
          onClick={() => window.location.href = '/'}
        />
      </div>
      <Card className="w-full max-w-md mx-auto border border-[#E2E8F0]">
        <CardHeader className="pb-4">
          <CardTitle className="text-2xl font-semibold text-center">Welcome</CardTitle>
          <CardDescription className="text-center">
            Sign in to your account to continue
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-5">
          {/* Password Sign In */}
          {activeTab === 'password' && (
            <PasswordSignIn
              error={error}
              isLoading={isLoading}
              showSignUp={showSignUp}
              setShowSignUp={setShowSignUp}
            />
          )}

          {/* Email Magic Link Sign In */}
          {activeTab === 'email' && (
            <EmailMagicLinkSignIn
              error={error}
              isLoading={isLoading}
              setActiveTab={setActiveTab}
              setShowSignUp={setShowSignUp}
            />
          )}

          {/* Google Sign In (button only, layout unchanged) */}
          {activeTab === 'password' && !showSignUp && (
            <>
              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-card px-2 text-muted-foreground">Or continue with</span>
                </div>
              </div>
              <GoogleSignInButton />
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}