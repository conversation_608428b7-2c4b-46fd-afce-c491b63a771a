import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Calendar, Clock, MapPin, Users, Edit, Share2, Printer, Copy, Check, Activity, Eye, Calendar as CalendarIcon, MailPlus, Inbox, Mail, Phone, X, Download, MessageSquare, Loader2, LockIcon, AlertTriangle } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import QRCode from "react-qr-code";
import LabelPrinter from "@/components/LabelPrinter";
import { useEvent } from "@/hooks/useEvent";
import { useInvites } from "@/hooks/useInvites";
import { useInviteActivity } from "@/hooks/useInviteActivity";
import { useSendInviteEmail } from "@/hooks/useSendInviteEmail";
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout";
import { FormatDate } from "@/lib/dayjs";
import { Format24to12 } from "@/lib/time";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { generateRsvpLink, generateDownloadInviteLink } from "@/lib/utils";
import { Header } from "@/components/Header";
import { isInviteManagementLocked, getEventLockMessage } from "@/lib/event/eventLock";

export default function InviteDetailsScreen() {
  const router = useRouter();
  const { toast } = useToast();
  const { data: session } = useSession();
  const { eventId, inviteId } = router.query;
  const { event, loading: eventLoading } = useEvent(eventId as string);
  const { invites, loading: invitesLoading } = useInvites(eventId as string);
  const { activities, loading: activitiesLoading, error: activitiesError } = useInviteActivity(eventId as string, inviteId as string);
  const { sendEmail, sendingEmail } = useSendInviteEmail();
  const [invite, setInvite] = useState<any>(null);
  const [copied, setCopied] = useState(false);
  const rsvpLink = invite ? generateRsvpLink(invite) : '';

  useEffect(() => {
    if (invites && inviteId) {
      const foundInvite = invites.find((i) => i.ID === inviteId);
      setInvite(foundInvite);
    }
  }, [invites, inviteId]);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(rsvpLink);
    setCopied(true);
    toast({
      title: "Link copied!",
      description: "The RSVP link has been copied to your clipboard.",
    });
    setTimeout(() => setCopied(false), 2000);
  };

  const handleShare = async () => {
    // Include the URL in the text itself to ensure it's shared properly
    const shareText = `RSVP for ${event?.eventName}\n${rsvpLink}`;

    if (navigator.share) {
      try {
        // Don't include the URL parameter, as some platforms prioritize it over text
        // Instead, include the URL in the text itself
        await navigator.share({
          title: `RSVP for ${event?.eventName}`,
          text: shareText,
        });
        toast({
          title: "Shared successfully!",
          description: "Your invite has been shared.",
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback to copy the formatted text instead of just the link
      navigator.clipboard.writeText(shareText);
      setCopied(true);
      toast({
        title: "Text copied!",
        description: "The invite text has been copied to your clipboard.",
      });
      setTimeout(() => setCopied(false), 2000);
    }
  };

  // Handle download invite - opens in a new tab
  const downloadInvite = () => {
    if (!invite || !eventId || !inviteId) return;

    // Generate API URL with print settings
    const apiUrl = generateDownloadInviteLink({
      eventId: eventId as string,
      ID: inviteId as string
    }, event?.printSettings);

    // Open the URL in a new tab
    window.open(apiUrl, '_blank');

    toast({
      title: "Downloading invite",
      description: "Your invite is downloading in a new tab.",
    });
  };

  // Handle send email function
  const handleSendEmail = () => {
    if (invite?.email) {
      sendEmail(eventId as string, inviteId as string, invite.email);
    }
  };

  // Helper function to format activity type into human-readable text
  const getActivityDescription = (activity: any) => {
    switch (activity.type) {
      case 'invite_link_opened':
        return 'RSVP link was opened';
      case 'rsvp_initiated':
        return 'RSVP process was started';
      case 'rsvp_form_submitted':
      case 'rsvp_response':
        if (activity.properties?.status === 'accepted') {
          return <>Invitation <span className="text-green-800">Accepted</span></>;
        } else if (activity.properties?.status === 'declined') {
          return <>Invitation <span className="text-red-800">Declined</span></>;
        } else {
          return `RSVP response: ${activity.properties?.status || 'unknown'}`;
        }
      case 'add_to_calendar_clicked':
        return 'Added event to calendar';
      default:
        return activity.type.replace(/_/g, ' ');
    }
  };

  // Helper function to get an icon for each activity type
  const getActivityIcon = (activity: any) => {
    switch (activity.type) {
      case 'invite_link_opened':
        return <Eye className="h-4 w-4 text-blue-500" />;
      case 'rsvp_initiated':
        return <Inbox className="h-4 w-4 text-purple-500" />;
      case 'rsvp_form_submitted':
      case 'rsvp_response':
        if (activity.properties?.status === 'accepted') {
          return <Check className="h-4 w-4 text-green-500" />;
        } else if (activity.properties?.status === 'declined') {
          return <X className="h-4 w-4 text-red-500" />;
        } else {
          return <MailPlus className="h-4 w-4 text-green-500" />;
        }
      case 'add_to_calendar_clicked':
        return <CalendarIcon className="h-4 w-4 text-orange-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  // Helper function to format the timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (e) {
      return timestamp;
    }
  };

  if (eventLoading || invitesLoading) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <Skeleton className="w-[400px] h-[600px]" />
        </div>
      </ProtectedLayout>
    );
  }

  if (!event || !invite) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <p className="text-gray-500">Invite not found</p>
        </div>
      </ProtectedLayout>
    );
  }

  return (
    <ProtectedLayout>
      <div className="flex flex-col  bg-gray-50">
        {/* Use the shared Header component with breadcrumbs */}
        <Header
          title="Invite Details"
          breadcrumbs={[
            { label: 'Events', href: '/events' },
            { label: event?.eventName || 'Event Details', href: `/event/${eventId}` },
            { label: 'Invites', href: `/event/${eventId}/invites` },
          ]}
        />

        {/* Content - Updated to use 2-column layout on non-mobile screens */}
        <div className="flex-1 p-4 pb-20">
          <div className="container mx-auto">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Primary Column (Invite Details) */}
              <div className="w-full md:w-2/3 space-y-4">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <h2 className="text-2xl font-bold">{invite.name}</h2>
                        <div className="flex items-center gap-2">
                        <Users className="h-5 w-5 text-gray-500" />
                        <span>{invite.adults} Adults {invite.children} Children</span>
                      </div>
                      </div>
                      <div className="w-24 h-24">
                        <QRCode
                          size={96}
                          value={rsvpLink}
                        />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* RSVP Status and Response Time */}
                      {invite.status !== 'invited' && (
                        <div className="flex flex-col gap-1 mt-1">
                          <div className="flex items-center gap-2">
                            <Badge className={`text-xs ${
                              invite.status === 'accepted' ? 'bg-green-100 text-green-800' :
                              invite.status === 'invited' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {invite.status.toUpperCase()}
                            </Badge>
                            {invite.response?.timestamp && (
                              <div className="flex items-center gap-1 text-xs text-gray-500">
                                <Clock className="h-3.5 w-3.5" />
                                <span>Responded {formatTimestamp(invite.response.timestamp)}</span>
                              </div>
                            )}
                          </div>
                          {invite.response && (
                            <div className="flex items-center gap-2 mt-1">
                              <Users className="h-5 w-5 text-gray-500" />
                              <span>
                                {invite.response.adults} Adults {invite.response.children} Children Confirmed
                              </span>
                            </div>
                          )}
                        </div>
                      )}
                      {invite.status === 'invited' && (
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            AWAITING RESPONSE
                          </Badge>
                        </div>
                      )}

                      {/* Contact Information */}
                      <div className="border-t pt-4 mt-2">
                        <h3 className="font-semibold mb-2">Contact Information</h3>
                        <div className="space-y-2">
                          {invite.email && (
                            <div className="flex items-center gap-2">
                              <Mail className="h-5 w-5 text-gray-500" />
                              <a href={`mailto:${invite.email}`} className="text-blue-600 hover:underline">{invite.email}</a>
                            </div>
                          )}
                          {invite.phone && (
                            <div className="flex items-center gap-2">
                              <Phone className="h-5 w-5 text-gray-500" />
                              <a href={`tel:${invite.phone}`} className="text-blue-600 hover:underline">{invite.phone}</a>
                            </div>
                          )}
                          {!invite.email && !invite.phone && (
                            <p className="text-sm text-gray-500">No contact information available</p>
                          )}
                        </div>
                      </div>

                      {/* Guest Message */}
                      {invite.response?.message && (
                        <div className="border-t pt-4">
                          <h3 className="font-semibold mb-2">Guest Message</h3>
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <p className="text-sm">{invite.response.message}</p>
                            {invite.response.timestamp && (
                              <p className="text-xs text-gray-500 mt-1">
                                {new Date(invite.response.timestamp).toLocaleString()}
                              </p>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Messages */}
                      {invite.message && invite.message.length > 0 && (
                        <div className="border-t pt-4">
                          <h3 className="font-semibold mb-2">Messages</h3>
                          <div className="space-y-2">
                            {invite.message.map((msg: any, index: number) => (
                              <div key={index} className="bg-gray-50 p-3 rounded-lg">
                                <p className="text-sm">{msg.content}</p>
                                <p className="text-xs text-gray-500 mt-1">
                                  {new Date(msg.timestamp).toLocaleString()}
                                </p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    {isInviteManagementLocked(event) ? (
                      <div className="flex items-center">
                        <Button
                          variant="outline"
                          className="cursor-not-allowed opacity-60"
                          disabled={true}
                          title={getEventLockMessage(event) || "Invite management locked"}
                        >
                          <LockIcon className="h-4 w-4 mr-2" />
                          Edit Invite
                        </Button>
                      </div>
                    ) : (
                      <Button
                        variant="outline"
                        onClick={() => router.push(`/event/${eventId}/invites/${inviteId}/edit`)}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Invite
                      </Button>
                    )}
                    {/* <Button
                      variant="outline"
                      onClick={() => router.push(`/event/${eventId}/invites/${inviteId}/print`)}
                    >
                      <Printer className="h-4 w-4 mr-2" />
                      Print Invite
                    </Button> */}
                  </CardFooter>
                </Card>
              </div>

              {/* Secondary Column (RSVP Link, Activity, Danger Zone) */}
              <div className="w-full md:w-1/3 space-y-4">
                {/* RSVP Link */}
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">RSVP Link</h3>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex gap-2">
                        <Input
                          readOnly
                          value={rsvpLink}
                          className="flex-1"
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={handleCopy}
                          className="shrink-0"
                        >
                          {copied ? (
                            <Check className="h-4 w-4 text-green-500" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                      <p className="text-sm text-gray-500">
                        Copy link and share it with your guests
                      </p>

                      {/* Action buttons */}
                      <div className="grid grid-cols-2 gap-3 w-full">
                        <Button
                          variant="outline"
                          onClick={downloadInvite}
                          className="flex items-center justify-center"
                          disabled={isInviteManagementLocked(event)}
                          title={isInviteManagementLocked(event) ? (getEventLockMessage(event) || "Invite management locked") : ""}
                        >
                          {isInviteManagementLocked(event) ? <LockIcon className="h-4 w-4 mr-2" /> : <Download className="h-4 w-4 mr-2" />}
                          Download Invite
                        </Button>
                        <Button
                          variant="outline"
                          onClick={handleShare}
                          className="flex items-center justify-center"
                          disabled={isInviteManagementLocked(event)}
                          title={isInviteManagementLocked(event) ? (getEventLockMessage(event) || "Invite management locked") : ""}
                        >
                          {isInviteManagementLocked(event) ? <LockIcon className="h-4 w-4 mr-2" /> : <Share2 className="h-4 w-4 mr-2" />}
                          Share Invite
                        </Button>
                      </div>

                      {/* Availability message */}
                      {!invite?.email && !invite?.phone && (
                        <div className="w-full py-1 px-4 bg-yellow-50 border border-yellow-100 text-center rounded-md text-sm">
                          Email & phone number is not available
                        </div>
                      )}

                      {/* Send invitation buttons */}
                      <Button
                        variant="outline"
                        onClick={() => handleSendEmail()}
                        className="flex items-center justify-center w-full mb-3"
                        disabled={!invite?.email || sendingEmail || isInviteManagementLocked(event)}
                        title={isInviteManagementLocked(event) ? (getEventLockMessage(event) || "Invite management locked") : ""}
                      >
                        {sendingEmail ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Sending...
                          </>
                        ) : isInviteManagementLocked(event) ? (
                          <>
                            <LockIcon className="h-4 w-4 mr-2" />
                            <span className="hidden sm:inline">Send invitation over email</span>
                            <span className="sm:hidden">Email Invite</span>
                          </>
                        ) : (
                          <>
                            <Mail className="h-4 w-4 mr-2" />
                            <span className="hidden sm:inline">Send invitation over email</span>
                            <span className="sm:hidden">Email Invite</span>
                          </>
                        )}
                      </Button>

                      {/* Phone availability message */}
                      {invite?.email && !invite?.phone && (
                        <div className="w-full py-1 px-4 bg-yellow-50 border border-yellow-100 text-center rounded-md text-sm mb-3">
                          Phone number is not available
                        </div>
                      )}

                      <Button
                        variant="outline"
                        className="flex items-center justify-center w-full"
                        disabled={true}
                      >
                        <MessageSquare className="h-4 w-4 mr-2" />
                        <span className="hidden sm:inline">Send invitation as a text</span>
                        <span className="sm:hidden">Text Invite</span>
                        <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">Coming Soon</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Activity History */}
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <Activity className="h-5 w-5 text-gray-500" />
                      Activity History
                    </h3>
                  </CardHeader>
                  <CardContent>
                    {activitiesLoading ? (
                      <div className="space-y-2">
                        <Skeleton className="h-6 w-full" />
                        <Skeleton className="h-6 w-full" />
                        <Skeleton className="h-6 w-full" />
                      </div>
                    ) : activitiesError ? (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-red-500">
                          <AlertTriangle className="h-5 w-5" />
                          <p className="font-medium">Error loading activity</p>
                        </div>
                        <p className="text-sm text-gray-600">{activitiesError.message}</p>
                        <p className="text-sm text-gray-500 mt-2">
                          This could be due to permission issues. Please make sure you have the correct permissions to view this invite&apos;s activity.
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={() => router.reload()}
                        >
                          <Loader2 className="h-4 w-4 mr-2" />
                          Retry
                        </Button>
                      </div>
                    ) : activities.length === 0 ? (
                      <p className="text-sm text-gray-500">No activity recorded for this invite yet.</p>
                    ) : (
                      <div className="space-y-3">
                        {activities.map((activity, index) => (
                          <div key={index} className="flex items-start gap-3 pb-3 border-b border-gray-100">
                            <div className="bg-gray-100 p-2 rounded-full">
                              {getActivityIcon(activity)}
                            </div>
                            <div className="flex-1">
                              <p className="font-medium">{getActivityDescription(activity)}</p>
                              <div className="text-sm text-gray-500">
                                {formatTimestamp(activity.timestamp)}
                              </div>

                              {activity.type === 'rsvp_form_submitted' && activity.properties?.status === 'accepted' && (activity.properties?.adults !== undefined || activity.properties?.children !== undefined) && (
                                <div className="text-sm mt-1">
                                  {activity.properties.adults !== undefined && `${activity.properties.adults} Adults`}
                                  {activity.properties.adults !== undefined && activity.properties.children !== undefined && ', '}
                                  {activity.properties.children !== undefined && `${activity.properties.children} Children`}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Danger Zone - Only visible to event owners */}
                {session?.user && (
                  (event.ownerEmail === session.user.email || event.ownerAccountId === session.user.id) && (
                    <Card className="border-red-200">
                      <CardHeader>
                        <h3 className="text-lg font-semibold text-red-600">Danger Zone</h3>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-500 mb-4">
                          Once you delete an invite, there is no going back. Please be certain.
                        </p>
                        {isInviteManagementLocked(event) ? (
                          <Button
                            variant="outline"
                            className="cursor-not-allowed opacity-60"
                            disabled={true}
                            title={getEventLockMessage(event) || "Invite management locked"}
                          >
                            <LockIcon className="h-4 w-4 mr-2" />
                            Delete Invite
                          </Button>
                        ) : (
                          <Button
                            variant="destructive"
                            className="text-white"
                            onClick={async () => {
                              if (window.confirm('Are you sure you want to delete this invite?')) {
                                try {
                                  const response = await fetch(`/api/event/${eventId}/invites/${inviteId}`, {
                                    method: 'DELETE',
                                  });
                                  const data = await response.json();

                                  if (!response.ok) {
                                    throw new Error(data.error || 'Failed to delete invite');
                                  }

                                  toast({
                                    title: "Invitation Deleted",
                                    description: `Invite "${data.invite.name}" (${data.invite.id}) deleted successfully`,
                                  });
                                  router.push(`/event/${eventId}/invites`);
                                } catch (error) {
                                  console.error('Error deleting invite:', error);
                                  toast({
                                    title: "Error",
                                    description: error instanceof Error ? error.message : "Failed to delete invite. Please try again.",
                                    variant: "destructive",
                                    className: "text-white",
                                  });
                                }
                              }
                            }}
                          >
                            Delete Invite
                          </Button>
                        )}
                      </CardContent>
                    </Card>
                  )
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  );
}