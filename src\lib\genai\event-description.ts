/**
 * Event description generation using GenAI
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import { log } from '@/lib/logger';
import {
  GenAIGenerationRequest,
  EventDescriptionResult,
  GenAIConfig,
} from './types';
import {
  EVENT_DESCRIPTION_PROMPT,
  fillPromptTemplate,
} from './prompts';
import {
  DEFAULT_GENAI_CONFIG,
  GENERATION_CONFIG,
} from './config';

export class EventDescriptionGenerator {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private config: GenAIConfig;

  constructor(config?: Partial<GenAIConfig>) {
    this.config = { ...DEFAULT_GENAI_CONFIG, ...config };
    this.genAI = new GoogleGenerativeAI(this.config.apiKey);
    this.model = this.genAI.getGenerativeModel({ 
      model: this.config.model,
      generationConfig: {
        maxOutputTokens: this.config.maxTokens,
        temperature: this.config.temperature,
        topP: this.config.topP,
        topK: this.config.topK,
      },
    });
  }

  /**
   * Generate event description using Gemini AI
   */
  async generate(request: GenAIGenerationRequest): Promise<EventDescriptionResult> {
    try {
      // Fill prompt template with event context and options
      const prompt = fillPromptTemplate(
        EVENT_DESCRIPTION_PROMPT,
        request.context,
        request.options || {}
      );

      log('Generating event description with Gemini', {
        eventId: request.eventId,
        tone: request.options?.tone,
        length: request.options?.length,
      });

      // Generate description using Gemini
      const description = await this.generateDescriptionWithGemini(prompt);

      // Parse and structure the response
      const result = this.parseDescriptionResponse(description, request);

      log('Event description generated successfully', {
        eventId: request.eventId,
        descriptionLength: result.description.length,
        tone: result.tone,
      });

      return result;

    } catch (error) {
      log('Event description generation failed', { error, request });
      throw new Error(`Failed to generate event description: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate description content using Gemini AI
   */
  private async generateDescriptionWithGemini(prompt: { system: string; user: string }): Promise<string> {
    try {
      // Combine system and user prompts
      const fullPrompt = `${prompt.system}\n\n${prompt.user}`;

      // Generate content with timeout
      const result = await Promise.race([
        this.model.generateContent(fullPrompt),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Generation timeout')), GENERATION_CONFIG.timeout)
        ),
      ]);

      const response = await result.response;
      const text = response.text();

      if (!text) {
        throw new Error('Empty response from Gemini');
      }

      return text.trim();

    } catch (error) {
      log('Gemini description generation failed', { error });
      throw new Error(`Gemini generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse and structure the description response
   */
  private parseDescriptionResponse(
    description: string, 
    request: GenAIGenerationRequest
  ): EventDescriptionResult {
    try {
      // Clean up the description
      const cleanDescription = this.cleanDescription(description);

      // Generate title if not present
      const title = this.extractOrGenerateTitle(cleanDescription, request.context.eventName);

      // Generate short description (first sentence or first 150 characters)
      const shortDescription = this.generateShortDescription(cleanDescription);

      // Extract keywords
      const keywords = this.extractKeywords(cleanDescription, request.context);

      return {
        title,
        description: cleanDescription,
        shortDescription,
        keywords,
        tone: request.options?.tone || 'friendly',
      };

    } catch (error) {
      log('Description parsing failed', { error });
      throw new Error(`Description parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Clean and format the description
   */
  private cleanDescription(description: string): string {
    // Remove extra whitespace and normalize line breaks
    let cleaned = description.replace(/\s+/g, ' ').trim();
    
    // Ensure proper paragraph breaks
    cleaned = cleaned.replace(/\. ([A-Z])/g, '.\n\n$1');
    
    // Remove any markdown formatting that might have been generated
    cleaned = cleaned.replace(/\*\*(.*?)\*\*/g, '$1');
    cleaned = cleaned.replace(/\*(.*?)\*/g, '$1');
    
    return cleaned;
  }

  /**
   * Extract or generate a title from the description
   */
  private extractOrGenerateTitle(description: string, eventName: string): string {
    // Look for a title in the first line
    const lines = description.split('\n');
    const firstLine = lines[0].trim();
    
    // If first line looks like a title (short and doesn't end with period)
    if (firstLine.length < 100 && !firstLine.endsWith('.')) {
      return firstLine;
    }
    
    // Otherwise, use the event name
    return eventName;
  }

  /**
   * Generate a short description (summary)
   */
  private generateShortDescription(description: string): string {
    // Get first sentence
    const firstSentence = description.split('.')[0] + '.';
    
    // If first sentence is too long, truncate to 150 characters
    if (firstSentence.length > 150) {
      return firstSentence.substring(0, 147) + '...';
    }
    
    return firstSentence;
  }

  /**
   * Extract relevant keywords from the description and context
   */
  private extractKeywords(description: string, context: any): string[] {
    const keywords: string[] = [];
    
    // Add event-specific keywords
    keywords.push(context.eventName.toLowerCase());
    keywords.push(context.host.toLowerCase());
    
    // Extract location-based keywords
    if (context.location) {
      const locationWords = context.location.split(/\s+/).filter((word: string) => word.length > 2);
      keywords.push(...locationWords.map((word: string) => word.toLowerCase()));
    }
    
    // Extract common event keywords from description
    const eventKeywords = [
      'party', 'celebration', 'gathering', 'event', 'conference', 'meeting',
      'workshop', 'seminar', 'networking', 'social', 'dinner', 'lunch',
      'birthday', 'wedding', 'anniversary', 'graduation', 'holiday'
    ];
    
    const descriptionLower = description.toLowerCase();
    eventKeywords.forEach(keyword => {
      if (descriptionLower.includes(keyword)) {
        keywords.push(keyword);
      }
    });
    
    // Remove duplicates and return
    return [...new Set(keywords)].slice(0, 10); // Limit to 10 keywords
  }

  /**
   * Generate fallback description if AI generation fails
   */
  private generateFallbackDescription(request: GenAIGenerationRequest): EventDescriptionResult {
    const { context } = request;
    const tone = request.options?.tone || 'friendly';
    
    let description = '';
    
    if (tone === 'formal') {
      description = `You are cordially invited to ${context.eventName}, hosted by ${context.host}. `;
      description += `This event will take place on ${context.eventDate.toLocaleDateString()} `;
      description += `from ${context.start} to ${context.end} at ${context.location}. `;
      if (context.message) {
        description += `${context.message} `;
      }
      description += `We look forward to your attendance.`;
    } else {
      description = `Join us for ${context.eventName}! ${context.host} is hosting this exciting event `;
      description += `on ${context.eventDate.toLocaleDateString()} from ${context.start} to ${context.end} `;
      description += `at ${context.location}. `;
      if (context.message) {
        description += `${context.message} `;
      }
      description += `Don't miss out on this great opportunity to connect and have fun!`;
    }
    
    return {
      title: context.eventName,
      description,
      shortDescription: `Join us for ${context.eventName} on ${context.eventDate.toLocaleDateString()}.`,
      keywords: [context.eventName.toLowerCase(), 'event', 'invitation'],
      tone,
    };
  }
}
