"use client"

import { SavedContactGroup } from "@/types";
import { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Search } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface GroupSelectorProps {
  contactGroups: SavedContactGroup[];
  selectedContacts?: string[];
  onChange: (selectedIds: string[]) => void;
  existingInvites?: any[];
}

export function GroupSelector({
  contactGroups,
  selectedContacts = [],
  onChange,
  existingInvites = []
}: GroupSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [topPosition, setTopPosition] = useState(0);
  const searchRef = useRef<HTMLDivElement>(null);
  const infoRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updatePosition = () => {
      if (searchRef.current && infoRef.current) {
        const searchHeight = searchRef.current.offsetHeight;
        const infoHeight = infoRef.current.offsetHeight;
        setTopPosition(searchHeight + infoHeight);
      }
    };

    updatePosition();
    window.addEventListener('resize', updatePosition);
    return () => window.removeEventListener('resize', updatePosition);
  }, []);

  // Check if a contact can be imported
  const canContactBeImported = (contact: any) => {
    if (!contact.email && !contact.phone) {
      return false;
    }

    const isDuplicate = existingInvites.some(invite => {
      // Check for email match (only if both have email)
      if (contact.email && invite.email &&
          contact.email.toLowerCase().trim() === invite.email.toLowerCase().trim()) {
        console.log(`Duplicate found by email: ${contact.email} matches ${invite.email}`);
        return true;
      }
      // Check for phone match (only if both have phone)
      if (contact.phone && invite.phone &&
          contact.phone.replace(/\D/g, '') === invite.phone.replace(/\D/g, '')) {
        console.log(`Duplicate found by phone: ${contact.phone} matches ${invite.phone}`);
        return true;
      }
      return false;
    });

    // Debug logging
    if (isDuplicate) {
      console.log(`Contact ${contact.name || contact.email || contact.phone} marked as duplicate`);
    } else {
      console.log(`Contact ${contact.name || contact.email || contact.phone} is available for import`);
    }

    return !isDuplicate;
  };

  // Generate unique contact ID
  const getContactId = (groupId: string, contactIndex: number) => {
    return `${groupId}-${contactIndex}`;
  };

  // Filter groups based on search query
  const filteredGroups = contactGroups?.filter(group =>
    group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    group.contacts.some(contact =>
      (contact.name && contact.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (contact.email && contact.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (contact.phone && contact.phone.includes(searchQuery))
    )
  ) || [];

  const totalSelected = selectedContacts.length;

  // Toggle individual contact selection
  const toggleContact = (contactId: string) => {
    const newSelected = selectedContacts.includes(contactId)
      ? selectedContacts.filter(id => id !== contactId)
      : [...selectedContacts, contactId];

    onChange(newSelected);
  };

  // Toggle group selection (select/deselect all contacts in group)
  const toggleGroup = (groupId: string) => {
    const group = contactGroups.find(g => g.id === groupId);
    if (!group) return;

    const groupContactIds = group.contacts
      .map((contact, index) => getContactId(groupId, index))
      .filter((contactId, index) => canContactBeImported(group.contacts[index]));

    const allGroupContactsSelected = groupContactIds.every(id => selectedContacts.includes(id));

    let newSelected;
    if (allGroupContactsSelected) {
      // Deselect all contacts in this group
      newSelected = selectedContacts.filter(id => !groupContactIds.includes(id));
    } else {
      // Select all importable contacts in this group
      newSelected = [...new Set([...selectedContacts, ...groupContactIds])];
    }

    onChange(newSelected);
  };

  // Check if group is selected (all importable contacts selected)
  const isGroupSelected = (groupId: string) => {
    const group = contactGroups.find(g => g.id === groupId);
    if (!group) return false;

    const groupContactIds = group.contacts
      .map((contact, index) => getContactId(groupId, index))
      .filter((contactId, index) => canContactBeImported(group.contacts[index]));

    return groupContactIds.length > 0 && groupContactIds.every(id => selectedContacts.includes(id));
  };
  // Check if group is partially selected
  const isGroupPartiallySelected = (groupId: string) => {
    const group = contactGroups.find(g => g.id === groupId);
    if (!group) return false;

    const groupContactIds = group.contacts
      .map((contact, index) => getContactId(groupId, index))
      .filter((contactId, index) => canContactBeImported(group.contacts[index]));

    const selectedInGroup = groupContactIds.filter(id => selectedContacts.includes(id));
    return selectedInGroup.length > 0 && selectedInGroup.length < groupContactIds.length;
  };

  // Get all importable contact IDs from all filtered groups
  const getAllImportableContactIds = () => {
    const allContactIds: string[] = [];
    
    filteredGroups.forEach(group => {
      group.contacts.forEach((contact, index) => {
        if (canContactBeImported(contact)) {
          allContactIds.push(getContactId(group.id, index));
        }
      });
    });
    
    return allContactIds;
  };

  // Check if all importable contacts are selected
  const areAllContactsSelected = () => {
    const allImportableIds = getAllImportableContactIds();
    return allImportableIds.length > 0 && allImportableIds.every(id => selectedContacts.includes(id));
  };

  // Toggle select all contacts
  const toggleSelectAll = () => {
    const allImportableIds = getAllImportableContactIds();
    
    if (areAllContactsSelected()) {
      // Deselect all
      onChange([]);
    } else {
      // Select all importable contacts
      onChange(allImportableIds);
    }
  };

  return (
    <div className="h-full relative">
      <div className="text-md font-medium mb-1 -mt-2">Select Groups</div>
      <div ref={searchRef} className="relative flex items-center">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
        <Input
          placeholder="Search groups..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>      <div ref={infoRef} className="mt-1">
        <div className="text-sm text-muted-foreground font-normal">
          Showing {filteredGroups.length} groups. {totalSelected} contacts selected
        </div>
        <div className="flex items-center gap-2 mt-2">
          <Checkbox
            id="select-all"
            checked={areAllContactsSelected()}
            onCheckedChange={toggleSelectAll}
            disabled={getAllImportableContactIds().length === 0}
          />
          <label htmlFor="select-all" className="text-sm text-muted-foreground font-normal cursor-pointer">
            Select All
          </label>
        </div>
      </div>

      <div
        className="absolute w-full overflow-y-auto bg-white pt-3"
        style={{
          top: topPosition + 59,
          bottom: 0,
        }}
      >
        <div className="border border-gray-200 rounded-lg bg-white">
          <Accordion type="multiple" className="w-full" defaultValue={filteredGroups.map(group => group.id)}>
            {filteredGroups.map((group, index) => {
              const isLastGroup = index === filteredGroups.length - 1;

              return (
                <AccordionItem
                  key={group.id}
                  value={group.id}
                  className={cn(
                    "border-b-0",
                    !isLastGroup && "border-b border-gray-200"
                  )}
                >
                  <div className="flex items-center space-x-3 w-full py-3 px-4 border-b border-gray-200">
                    <Checkbox
                      checked={isGroupSelected(group.id)}
                      ref={(el) => {
                        if (el && isGroupPartiallySelected(group.id)) {
                          const checkbox = el.querySelector('input[type="checkbox"]') as HTMLInputElement;
                          if (checkbox) {
                            checkbox.indeterminate = true;
                          }
                        }
                      }}
                      onCheckedChange={() => toggleGroup(group.id)}
                      className="shrink-0"
                    />
                    <AccordionTrigger
                      className="hover:no-underline flex-1 text-left py-0 px-0 border-0"
                    >
                      <span className="text-sm font-semibold text-left flex-1">
                        {group.name}
                      </span>
                    </AccordionTrigger>
                  </div>
                  <AccordionContent className="pb-2">
                    <div className="space-y-0 ml-4">
                      {group.contacts.map((contact, contactIndex) => {
                        const contactId = getContactId(group.id, contactIndex);
                        const canImport = canContactBeImported(contact);

                        return (
                          <div key={contactIndex} className="flex items-center space-x-3 py-2">
                            <Checkbox
                              checked={selectedContacts.includes(contactId)}
                              onCheckedChange={() => toggleContact(contactId)}
                              id={`contact-${contactId}`}
                              className="shrink-0 mt-1"
                              disabled={!canImport}
                            />
                            <span
                              className={cn(
                                "text-sm cursor-pointer flex-1 mt-1",
                                canImport ? "text-gray-900" : "text-gray-400"
                              )}
                              onClick={() => canImport && toggleContact(contactId)}
                            >
                              {contact.name || contact.email || contact.phone || 'Unknown'}
                              {!canImport && (
                                <span className="text-xs text-gray-400 ml-2">
                                  (already exists or invalid)
                                </span>
                              )}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              );
            })}
          </Accordion>
        </div>
      </div>
    </div>
  );
}
