'use client'

import { <PERSON><PERSON> } from "@/components/Header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/router"
import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { debugLog } from "@/lib/logger"
import { calculateUpgradeCost, planPriceAmounts } from "@/lib/plans"
import { Badge } from "@/components/ui/badge"
import { ArrowUp, Info } from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import RemoteConfig from "@/lib/remoteConfig"

const plans = {
  host_plus: {
    name: "Host+",
    price: 10,
    description: "For growing events"
  },
  host_pro: {
    name: "Host Pro",
    price: 35,
    description: "For professional events"
  }
}

export default function PaymentPage() {
  const router = useRouter()
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const { plan, eventId, from_plan } = router.query
  const [isBetaMode, setIsBetaMode] = useState(false)

  // Initialize and check feature flags on component mount
  useEffect(() => {
    const checkBetaMode = async () => {
      const remoteConfig = RemoteConfig.getInstance();
      setIsBetaMode(remoteConfig.isBetaMode());
    };
    
    checkBetaMode();
  }, []);

  // Calculate upgrade price if upgrading from an existing plan
  const calculatePrice = () => {
    if (!selectedPlan) return 0;
    
    if (from_plan && typeof from_plan === 'string') {
      return calculateUpgradeCost(from_plan, plan as string);
    }
    
    return selectedPlan.price;
  }

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
    }
  }, [session, router])

  const selectedPlan = plan ? plans[plan as keyof typeof plans] : null
  const price = calculatePrice();
  const isUpgrade = from_plan && typeof from_plan === 'string';
  const originalPrice = selectedPlan?.price || 0;
  const savings = isUpgrade ? originalPrice - price : 0;

  const handlePayment = async () => {
    if (!selectedPlan || !session || !eventId) {
      debugLog('Missing required data for payment', { selectedPlan, hasSession: !!session, eventId });
      return;
    }

    setLoading(true)
    try {
      // If in beta mode, skip payment and create event directly
      if (isBetaMode) {
        debugLog('Beta mode active, skipping payment', { eventId, plan });
        
        // Create event with selected plan without requiring payment
        // Use the correct API endpoint: /api/event/[eventId] with PUT method
        const eventResponse = await fetch(`/api/event/${eventId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: 'active',  // Mark as active instead of pending_payment
            plan: plan,
          }),
        });

        if (!eventResponse.ok) {
          const error = await eventResponse.json();
          debugLog('Event update failed in beta mode', { error });
          throw new Error('Failed to update event');
        }

        // Redirect to event page
        router.push(`/event/${eventId}`);
        return;
      }

      // Normal payment flow for production
      debugLog('Creating checkout session', { eventId, plan, price, from_plan });

      // Create Stripe checkout session
      const checkoutResponse = await fetch('/api/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventId: eventId as string,
          plan: plan,
          price: price, // Use calculated price (may be reduced for upgrades)
          from_plan: from_plan || null
        }),
      })

      if (!checkoutResponse.ok) {
        const error = await checkoutResponse.json();
        debugLog('Checkout session creation failed', { error });
        throw new Error('Failed to create checkout session');
      }

      const { url } = await checkoutResponse.json()
      debugLog('Checkout session created successfully', { url });
      
      // Redirect to Stripe checkout
      window.location.href = url
    } catch (error) {
      console.error('Payment error:', error)
      // Handle error (show error message to user)
    } finally {
      setLoading(false)
    }
  }

  if (!selectedPlan || !eventId) {
    debugLog('Invalid payment page state', { selectedPlan, eventId });
    return (
      <>
        <Header showUserProfile={true} buttons={[]} />
        <div className="container mx-auto px-4 py-8">
          <Card>
            <CardHeader>
              <CardTitle>Invalid Request</CardTitle>
              <CardDescription>
                {!selectedPlan 
                  ? "Please select a valid plan from the pricing page."
                  : "No event ID provided. Please create an event first."}
              </CardDescription>
            </CardHeader>
            <CardFooter>
              <Button onClick={() => router.push('/pricing')}>
                Go to Pricing
              </Button>
            </CardFooter>
          </Card>
        </div>
      </>
    )
  }

  return (
    <>
      <Header showUserProfile={true} buttons={[]} />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Complete Your Purchase</CardTitle>
              <CardDescription>Review your order before proceeding to payment</CardDescription>
              {isBetaMode && (
                <Badge className="mt-2 px-3 py-1 bg-blue-100 text-blue-800">
                  BETA MODE
                </Badge>
              )}
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-semibold flex items-center">
                      {selectedPlan.name}
                      {isUpgrade && (
                        <Badge className="ml-2 bg-green-100 text-green-800">
                          <ArrowUp className="h-3 w-3 mr-1" />
                          Upgrade
                        </Badge>
                      )}
                    </h3>
                    <p className="text-sm text-gray-500">{selectedPlan.description}</p>
                  </div>
                  <div className="text-right">
                    {isBetaMode ? (
                      <div className="flex flex-col items-end">
                        <p className="text-2xl font-bold text-green-600">Free</p>
                        <p className="text-sm text-gray-500 line-through">${price.toFixed(2)}</p>
                        <p className="text-xs text-blue-600">Beta Promotion</p>
                      </div>
                    ) : isUpgrade ? (
                      <div className="flex flex-col items-end">
                        <p className="text-2xl font-bold">${price.toFixed(2)}</p>
                        <div className="flex items-center">
                          <p className="text-sm text-gray-500 line-through mr-1">${originalPrice.toFixed(2)}</p>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <Info className="h-3 w-3 text-gray-400" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="text-xs">
                                  You&apos;re upgrading from {from_plan === 'host_plus' ? 'Host+' : from_plan === 'host_pro' ? 'Host Pro' : 'Free'}.
                                  <br />Only paying the difference.
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <p className="text-xs text-green-600">You save ${savings.toFixed(2)}</p>
                      </div>
                    ) : (
                      <div>
                        <p className="text-2xl font-bold">${price.toFixed(2)}</p>
                        <p className="text-sm text-gray-500">per event</p>
                      </div>
                    )}
                  </div>
                </div>
                <div className="border-t pt-4">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold">Total</span>
                    {isBetaMode ? (
                      <span className="text-2xl font-bold text-green-600">Free</span>
                    ) : (
                      <span className="text-2xl font-bold">${price.toFixed(2)}</span>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button 
                variant="outline" 
                onClick={() => router.push('/pricing')}
              >
                Back to Pricing
              </Button>
              <Button 
                onClick={handlePayment}
                disabled={loading}
              >
                {loading ? "Processing..." : isBetaMode ? "Continue" : "Pay Now"}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </>
  )
}