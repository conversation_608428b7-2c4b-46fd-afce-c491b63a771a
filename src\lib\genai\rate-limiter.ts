/**
 * GenAI Rate Limiter - Manages rate limiting for AI generation requests
 */

import { Database } from '@/lib/database';
import { log } from '@/lib/logger';
import { GenAIRateLimit } from './types';
import { RATE_LIMITS, DB_COLLECTIONS, CACHE_KEYS } from './config';

export class GenAIRateLimiter {
  private db: Database;
  private cache: Map<string, GenAIRateLimit> = new Map();

  constructor() {
    this.db = Database.getInstance();
  }

  /**
   * Check if user can make a request and update usage
   */
  async checkAndUpdateLimit(userId: string, userType: 'free' | 'paid' | 'admin' = 'free'): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: Date;
    error?: string;
  }> {
    try {
      // Get current rate limit status
      const rateLimit = await this.getRateLimit(userId, userType);
      
      // Check if limit is exceeded
      if (rateLimit.currentUsage >= rateLimit.dailyLimit) {
        log('Rate limit exceeded', {
          userId,
          userType,
          currentUsage: rateLimit.currentUsage,
          dailyLimit: rateLimit.dailyLimit,
        });
        
        return {
          allowed: false,
          remaining: 0,
          resetTime: rateLimit.resetTime,
          error: `Daily limit of ${rateLimit.dailyLimit} AI generations exceeded. Resets at ${rateLimit.resetTime.toLocaleTimeString()}.`,
        };
      }
      
      // Update usage count
      rateLimit.currentUsage++;
      await this.updateRateLimit(rateLimit);
      
      const remaining = rateLimit.dailyLimit - rateLimit.currentUsage;
      
      log('Rate limit check passed', {
        userId,
        userType,
        currentUsage: rateLimit.currentUsage,
        remaining,
      });
      
      return {
        allowed: true,
        remaining,
        resetTime: rateLimit.resetTime,
      };
      
    } catch (error) {
      console.error('Rate limit check failed:', error);
      
      // In case of error, allow the request but log the issue
      return {
        allowed: true,
        remaining: 0,
        resetTime: new Date(),
        error: 'Rate limit check failed, allowing request',
      };
    }
  }

  /**
   * Get rate limit for user
   */
  private async getRateLimit(userId: string, userType: 'free' | 'paid' | 'admin'): Promise<GenAIRateLimit> {
    // Check cache first
    const cacheKey = CACHE_KEYS.RATE_LIMIT(userId);
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!;
      
      // Check if cache is still valid
      if (new Date(cached.resetTime) > new Date()) {
        return cached;
      }
    }
    
    // Get from database
    let rateLimit = await this.db.readData(DB_COLLECTIONS.AI_RATE_LIMITS, userId) as GenAIRateLimit | null;
    
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0); // Reset at midnight
    
    // Create new rate limit if doesn't exist or expired
    if (!rateLimit || new Date(rateLimit.resetTime) <= now) {
      const dailyLimit = this.getDailyLimit(userType);
      
      rateLimit = {
        userId,
        dailyLimit,
        currentUsage: 0,
        resetTime: tomorrow,
        userType,
      };
      
      await this.updateRateLimit(rateLimit);
    }
    
    // Update cache
    this.cache.set(cacheKey, rateLimit);
    
    return rateLimit;
  }

  /**
   * Update rate limit in database
   */
  private async updateRateLimit(rateLimit: GenAIRateLimit): Promise<void> {
    try {
      await this.db.addData(DB_COLLECTIONS.AI_RATE_LIMITS, {
        ID: rateLimit.userId,
        ...rateLimit,
      });
      
      // Update cache
      const cacheKey = CACHE_KEYS.RATE_LIMIT(rateLimit.userId);
      this.cache.set(cacheKey, rateLimit);
      
    } catch (error) {
      console.error('Failed to update rate limit:', error);
      throw error;
    }
  }

  /**
   * Get daily limit based on user type
   */
  private getDailyLimit(userType: 'free' | 'paid' | 'admin'): number {
    return RATE_LIMITS[userType].dailyLimit;
  }

  /**
   * Get per-event limit based on user type
   */
  private getPerEventLimit(userType: 'free' | 'paid' | 'admin'): number {
    return RATE_LIMITS[userType].perEventLimit;
  }

  /**
   * Check if user has AI access enabled
   */
  async checkAIAccess(userId: string): Promise<boolean> {
    try {
      // Get user from database to check AI access status
      const user = await this.db.readData('users', userId);
      
      if (!user) {
        log('User not found for AI access check', { userId });
        return false;
      }
      
      // Check if user has AI access enabled (default to false for security)
      const hasAiAccess = user.hasAiAccess === true;
      
      log('AI access check', { userId, hasAiAccess });
      
      return hasAiAccess;
      
    } catch (error) {
      console.error('AI access check failed:', error);
      // Default to false for security
      return false;
    }
  }

  /**
   * Check per-event generation limits
   */
  async checkEventLimit(eventId: string, userId: string, userType: 'free' | 'paid' | 'admin'): Promise<{
    allowed: boolean;
    remaining: number;
    error?: string;
  }> {
    try {
      // Get event usage from database
      const eventUsage = await this.getEventUsage(eventId);
      const perEventLimit = this.getPerEventLimit(userType);
      
      if (eventUsage >= perEventLimit) {
        return {
          allowed: false,
          remaining: 0,
          error: `Per-event limit of ${perEventLimit} generations exceeded for this event.`,
        };
      }
      
      return {
        allowed: true,
        remaining: perEventLimit - eventUsage,
      };
      
    } catch (error) {
      console.error('Event limit check failed:', error);
      // Allow request in case of error
      return {
        allowed: true,
        remaining: 0,
        error: 'Event limit check failed, allowing request',
      };
    }
  }

  /**
   * Get current usage count for an event
   */
  private async getEventUsage(eventId: string): Promise<number> {
    try {
      const event = await this.db.readData('events', eventId);
      
      if (!event || !event.aiUsageCounters) {
        return 0;
      }
      
      return event.aiUsageCounters.totalGenerations || 0;
      
    } catch (error) {
      console.error('Failed to get event usage:', error);
      return 0;
    }
  }

  /**
   * Increment event usage counter
   */
  async incrementEventUsage(eventId: string, generationType: 'digital-invite' | 'printable-invite' | 'event-description'): Promise<void> {
    try {
      const event = await this.db.readData('events', eventId);
      
      if (!event) {
        log('Event not found for usage increment', { eventId });
        return;
      }
      
      // Initialize usage counters if they don't exist
      if (!event.aiUsageCounters) {
        event.aiUsageCounters = {
          digitalInviteGenerations: 0,
          printableInviteGenerations: 0,
          descriptionGenerations: 0,
          totalGenerations: 0,
          lastGenerationDate: new Date(),
        };
      }
      
      // Increment specific counter
      switch (generationType) {
        case 'digital-invite':
          event.aiUsageCounters.digitalInviteGenerations++;
          break;
        case 'printable-invite':
          event.aiUsageCounters.printableInviteGenerations++;
          break;
        case 'event-description':
          event.aiUsageCounters.descriptionGenerations++;
          break;
      }
      
      // Increment total counter
      event.aiUsageCounters.totalGenerations++;
      event.aiUsageCounters.lastGenerationDate = new Date();
      
      // Update event in database
      await this.db.updateData('events', eventId, {
        aiUsageCounters: event.aiUsageCounters,
      });
      
      log('Event usage incremented', { eventId, generationType, totalGenerations: event.aiUsageCounters.totalGenerations });
      
    } catch (error) {
      console.error('Failed to increment event usage:', error);
    }
  }

  /**
   * Get usage statistics for a user
   */
  async getUserUsageStats(userId: string): Promise<{
    dailyUsage: number;
    remainingQuota: number;
    resetTime: Date;
    totalGenerations: number;
  }> {
    try {
      const rateLimit = await this.getRateLimit(userId, 'free'); // Default to free, will be updated based on actual user type
      
      return {
        dailyUsage: rateLimit.currentUsage,
        remainingQuota: rateLimit.dailyLimit - rateLimit.currentUsage,
        resetTime: rateLimit.resetTime,
        totalGenerations: rateLimit.currentUsage, // For now, same as daily usage
      };
      
    } catch (error) {
      console.error('Failed to get user usage stats:', error);
      return {
        dailyUsage: 0,
        remainingQuota: 0,
        resetTime: new Date(),
        totalGenerations: 0,
      };
    }
  }

  /**
   * Comprehensive rate limit check including AI access
   */
  async checkAccess(userId: string, userType: 'free' | 'paid' | 'admin' = 'free'): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: Date;
    error?: string;
  }> {
    // First check if user has AI access enabled
    const hasAccess = await this.checkAIAccess(userId);
    if (!hasAccess) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: new Date(),
        error: 'AI features are not enabled for your account. Please contact support.',
      };
    }
    
    // Then check rate limits
    return this.checkAndUpdateLimit(userId, userType);
  }
}
