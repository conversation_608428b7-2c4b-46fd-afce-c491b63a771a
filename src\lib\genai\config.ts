/**
 * Configuration and constants for GenAI service
 */

import { GenAIConfig } from './types';

// Default GenAI configuration
export const DEFAULT_GENAI_CONFIG: GenAIConfig = {
  apiKey: process.env.GEMINI_API_KEY || '',
  model: 'gemini-1.5-flash', // Use the latest Gemini model
  maxTokens: 8192,
  temperature: 0.7, // Balanced creativity and consistency
  topP: 0.8,
  topK: 40,
};

// Rate limiting configuration
export const RATE_LIMITS = {
  free: {
    dailyLimit: 5,
    perEventLimit: 3,
  },
  paid: {
    dailyLimit: 50,
    perEventLimit: 10,
  },
  admin: {
    dailyLimit: 1000,
    perEventLimit: 100,
  },
} as const;

// Image dimensions for different use cases
export const IMAGE_DIMENSIONS = {
  digitalInvite: {
    mobile: { width: 400, height: 300 },
    desktop: { width: 800, height: 600 },
    social: { width: 1200, height: 630 }, // Social media sharing
  },
  printableInvite: {
    A4: { 
      portrait: { width: 2480, height: 3508 },
      landscape: { width: 3508, height: 2480 }
    },
    A5: { 
      portrait: { width: 1748, height: 2480 },
      landscape: { width: 2480, height: 1748 }
    },
    A6: { 
      portrait: { width: 1240, height: 1748 },
      landscape: { width: 1748, height: 1240 }
    },
    photo4x6: { 
      portrait: { width: 1200, height: 1800 },
      landscape: { width: 1800, height: 1200 }
    },
    photo5x7: { 
      portrait: { width: 1500, height: 2100 },
      landscape: { width: 2100, height: 1500 }
    },
    photo6x8: { 
      portrait: { width: 1800, height: 2400 },
      landscape: { width: 2400, height: 1800 }
    },
    photo8x10: { 
      portrait: { width: 2400, height: 3000 },
      landscape: { width: 3000, height: 2400 }
    }
  }
} as const;

// Theme configurations
export const THEMES = {
  modern: {
    colors: ['#2563eb', '#3b82f6', '#60a5fa', '#93c5fd'],
    fonts: ['Inter', 'Roboto', 'system-ui'],
    style: 'clean, minimalist, geometric shapes',
  },
  classic: {
    colors: ['#1f2937', '#374151', '#6b7280', '#9ca3af'],
    fonts: ['Times New Roman', 'Georgia', 'serif'],
    style: 'traditional, elegant, serif typography',
  },
  elegant: {
    colors: ['#7c3aed', '#8b5cf6', '#a78bfa', '#c4b5fd'],
    fonts: ['Playfair Display', 'Crimson Text', 'serif'],
    style: 'sophisticated, luxurious, ornate details',
  },
  fun: {
    colors: ['#f59e0b', '#fbbf24', '#fcd34d', '#fde68a'],
    fonts: ['Comic Sans MS', 'Fredoka One', 'sans-serif'],
    style: 'playful, colorful, rounded shapes',
  },
  minimal: {
    colors: ['#000000', '#ffffff', '#f3f4f6', '#e5e7eb'],
    fonts: ['Helvetica', 'Arial', 'sans-serif'],
    style: 'ultra-minimal, black and white, lots of whitespace',
  },
} as const;

// Color schemes
export const COLOR_SCHEMES = {
  warm: ['#dc2626', '#ea580c', '#d97706', '#ca8a04'],
  cool: ['#0284c7', '#0891b2', '#059669', '#16a34a'],
  neutral: ['#374151', '#6b7280', '#9ca3af', '#d1d5db'],
  vibrant: ['#dc2626', '#7c3aed', '#0284c7', '#059669'],
} as const;

// Generation timeouts and retries
export const GENERATION_CONFIG = {
  timeout: 30000, // 30 seconds
  maxRetries: 3,
  retryDelay: 1000, // 1 second
  cacheTimeout: 3600000, // 1 hour
} as const;

// Error codes
export const ERROR_CODES = {
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  INVALID_API_KEY: 'INVALID_API_KEY',
  GENERATION_FAILED: 'GENERATION_FAILED',
  INVALID_INPUT: 'INVALID_INPUT',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  TIMEOUT: 'TIMEOUT',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

// Validation rules
export const VALIDATION_RULES = {
  eventName: {
    minLength: 1,
    maxLength: 100,
  },
  message: {
    minLength: 0,
    maxLength: 1000,
  },
  location: {
    minLength: 1,
    maxLength: 200,
  },
  host: {
    minLength: 1,
    maxLength: 100,
  },
} as const;

// Cache keys
export const CACHE_KEYS = {
  RATE_LIMIT: (userId: string) => `genai:rate_limit:${userId}`,
  GENERATION: (eventId: string, type: string) => `genai:generation:${eventId}:${type}`,
  USAGE_STATS: (userId: string) => `genai:usage:${userId}`,
} as const;

// Database collections
export const DB_COLLECTIONS = {
  AI_RATE_LIMITS: 'ai_rate_limits',
  AI_GENERATIONS: 'ai_generations',
  AI_USAGE_STATS: 'ai_usage_stats',
} as const;
