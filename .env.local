FROM_EMAIL=<EMAIL>
AWS_REGION=ap-southeast-2
AUTH_SECRET=wyOQQPTUILfaM9QCfESRLZfi2S5WRJPzaQQo/g+oDdY=
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Shortlink API Configuration
SHORTLINK_JWT_SECRET=f7a4c9e2b5d1836gho7k5j3i8l2m9n0pq
SHORTLINK_JWT_TOKEN=UsJILRwbOxoqXAxWzxtVl9_w4CzacoMQh5qq9p6l0gclkhuU-E4CkaAuoOPa_aTstKRvoYMl2jT6lPFyeRO3Ig

# Firebase Admin SDK Configuration
FIREBASE_PROJECT_ID=iamcoming-universe
FIREBASE_CLIENT_EMAIL=<EMAIL>
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Google OAuth
GOOGLE_CLIENT_ID=984892648933-qht5vr8cakch6h63r3ulbeb2av62o5jj.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

IAC_DEBUG=true

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51R8XPOPfHr585uT1NHawj4MrlJ1en6p7paY1XEnnUjxBJexTXPA12085elVxweOiE6VBSfx6E2dSkO0sN8KQw1el00Usg7Kz80
STRIPE_WEBHOOK_SECRET=whsec_KkzQ7ilyOZagQ9QQGRCrY5bwEZ1s1Opm

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=vJBEKBZbcrksMRSAVUyMPOIE3Ncq2jI5B/xHlgk7

# Google reCAPTCHA v3
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=6LeoiS0rAAAAACm9jZg9dv6oPfltU9Ij6v7CV217
RECAPTCHA_SECRET_KEY=6LeoiS0rAAAAAPh0P4TjcpH2TO0Y2UMxoHLvkuDX

# AI Configuration
# =================

# Gemini API Configuration
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=AIzaSyBi0nbGYo9CJTXa0y9FZjoOQk6EvFvBNpw