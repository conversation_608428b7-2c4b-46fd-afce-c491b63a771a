/**
 * Test page for GenAI service functionality
 */

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Loader2, Download, Eye, Sparkles } from 'lucide-react';

interface GenerationResult {
  success: boolean;
  data?: any;
  usage?: {
    tokensUsed: number;
    generationTime: number;
    remainingQuota: number;
  };
  error?: string;
}

export default function GenAITestPage() {
  const { data: session } = useSession();
  const { toast } = useToast();

  // Form state
  const [eventData, setEventData] = useState({
    eventName: 'Birthday Party',
    host: 'Sarah <PERSON>',
    eventDate: '2024-12-25',
    start: '18:00',
    end: '22:00',
    location: '123 Main Street, Anytown',
    message: 'Join us for a fun celebration with food, music, and great company!',
  });

  // Generation options
  const [digitalOptions, setDigitalOptions] = useState({
    theme: 'modern',
    colorScheme: 'vibrant',
    dimensions: { width: 800, height: 600 },
  });

  const [printableOptions, setPrintableOptions] = useState({
    paperSize: 'A5',
    orientation: 'portrait',
    theme: 'elegant',
    colorScheme: 'neutral',
  });

  const [descriptionOptions, setDescriptionOptions] = useState({
    tone: 'friendly',
    length: 'medium',
    includeDetails: true,
  });

  // Loading states
  const [loading, setLoading] = useState({
    digital: false,
    printable: false,
    description: false,
    usage: false,
  });

  // Results
  const [results, setResults] = useState<{
    digital?: GenerationResult;
    printable?: GenerationResult;
    description?: GenerationResult;
  }>({});

  const [usageStats, setUsageStats] = useState<any>(null);

  // Generate digital invite
  const generateDigitalInvite = async () => {
    if (!session?.user?.id) {
      toast({ title: 'Error', description: 'Please sign in to test AI features' });
      return;
    }

    setLoading(prev => ({ ...prev, digital: true }));
    
    try {
      const response = await fetch('/api/genai/digital-invite', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          eventId: 'test-event-' + Date.now(),
          context: {
            ...eventData,
            eventDate: new Date(eventData.eventDate),
          },
          options: digitalOptions,
        }),
      });

      const result = await response.json();
      setResults(prev => ({ ...prev, digital: result }));

      if (result.success) {
        toast({ title: 'Success', description: 'Digital invite generated successfully!' });
      } else {
        toast({ title: 'Error', description: result.error || 'Generation failed' });
      }
    } catch (error) {
      toast({ title: 'Error', description: 'Failed to generate digital invite' });
    } finally {
      setLoading(prev => ({ ...prev, digital: false }));
    }
  };

  // Generate printable invite
  const generatePrintableInvite = async () => {
    if (!session?.user?.id) {
      toast({ title: 'Error', description: 'Please sign in to test AI features' });
      return;
    }

    setLoading(prev => ({ ...prev, printable: true }));
    
    try {
      const response = await fetch('/api/genai/printable-invite', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          eventId: 'test-event-' + Date.now(),
          context: {
            ...eventData,
            eventDate: new Date(eventData.eventDate),
          },
          options: printableOptions,
        }),
      });

      const result = await response.json();
      setResults(prev => ({ ...prev, printable: result }));

      if (result.success) {
        toast({ title: 'Success', description: 'Printable invite generated successfully!' });
      } else {
        toast({ title: 'Error', description: result.error || 'Generation failed' });
      }
    } catch (error) {
      toast({ title: 'Error', description: 'Failed to generate printable invite' });
    } finally {
      setLoading(prev => ({ ...prev, printable: false }));
    }
  };

  // Generate event description
  const generateEventDescription = async () => {
    if (!session?.user?.id) {
      toast({ title: 'Error', description: 'Please sign in to test AI features' });
      return;
    }

    setLoading(prev => ({ ...prev, description: true }));
    
    try {
      const response = await fetch('/api/genai/event-description', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          eventId: 'test-event-' + Date.now(),
          context: {
            ...eventData,
            eventDate: new Date(eventData.eventDate),
          },
          options: descriptionOptions,
        }),
      });

      const result = await response.json();
      setResults(prev => ({ ...prev, description: result }));

      if (result.success) {
        toast({ title: 'Success', description: 'Event description generated successfully!' });
      } else {
        toast({ title: 'Error', description: result.error || 'Generation failed' });
      }
    } catch (error) {
      toast({ title: 'Error', description: 'Failed to generate event description' });
    } finally {
      setLoading(prev => ({ ...prev, description: false }));
    }
  };

  // Get usage statistics
  const getUsageStats = async () => {
    if (!session?.user?.id) return;

    setLoading(prev => ({ ...prev, usage: true }));

    try {
      const response = await fetch('/api/genai/usage-stats');
      const stats = await response.json();
      setUsageStats(stats);
    } catch (error) {
      console.error('Failed to get usage stats:', error);
    } finally {
      setLoading(prev => ({ ...prev, usage: false }));
    }
  };

  // Enable AI access for testing
  const enableAIAccess = async () => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch(`/api/admin/users/${session.user.id}/enable-ai-access`, {
        method: 'POST',
      });

      if (response.ok) {
        toast({ title: 'Success', description: 'AI access enabled for testing!' });
        await getUsageStats(); // Refresh stats
      } else {
        const error = await response.json();
        toast({ title: 'Error', description: error.error || 'Failed to enable AI access' });
      }
    } catch (error) {
      toast({ title: 'Error', description: 'Failed to enable AI access' });
    }
  };

  // Load usage stats on component mount
  React.useEffect(() => {
    if (session?.user?.id) {
      getUsageStats();
    }
  }, [session?.user?.id]);

  if (!session) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p>Please sign in to test GenAI features.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2">
        <Sparkles className="h-6 w-6 text-purple-600" />
        <h1 className="text-3xl font-bold">GenAI Service Test Page</h1>
      </div>

      {/* Usage Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Usage Statistics
            <Button 
              variant="outline" 
              size="sm" 
              onClick={getUsageStats}
              disabled={loading.usage}
            >
              {loading.usage ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Refresh'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {usageStats ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm text-gray-600">AI Access</p>
                <div className="flex items-center gap-2">
                  <Badge variant={usageStats.user.hasAiAccess ? 'default' : 'destructive'}>
                    {usageStats.user.hasAiAccess ? 'Enabled' : 'Disabled'}
                  </Badge>
                  {!usageStats.user.hasAiAccess && (
                    <Button size="sm" variant="outline" onClick={enableAIAccess}>
                      Enable for Testing
                    </Button>
                  )}
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-600">Daily Usage</p>
                <p className="font-semibold">{usageStats.user.dailyUsage}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Remaining Quota</p>
                <p className="font-semibold">{usageStats.user.remainingQuota}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">User Type</p>
                <Badge variant="outline">{usageStats.user.userType}</Badge>
              </div>
            </div>
          ) : (
            <p>Loading usage statistics...</p>
          )}
        </CardContent>
      </Card>

      {/* Event Data Form */}
      <Card>
        <CardHeader>
          <CardTitle>Event Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="eventName">Event Name</Label>
              <Input
                id="eventName"
                value={eventData.eventName}
                onChange={(e) => setEventData(prev => ({ ...prev, eventName: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="host">Host</Label>
              <Input
                id="host"
                value={eventData.host}
                onChange={(e) => setEventData(prev => ({ ...prev, host: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="eventDate">Date</Label>
              <Input
                id="eventDate"
                type="date"
                value={eventData.eventDate}
                onChange={(e) => setEventData(prev => ({ ...prev, eventDate: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                value={eventData.location}
                onChange={(e) => setEventData(prev => ({ ...prev, location: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="start">Start Time</Label>
              <Input
                id="start"
                type="time"
                value={eventData.start}
                onChange={(e) => setEventData(prev => ({ ...prev, start: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="end">End Time</Label>
              <Input
                id="end"
                type="time"
                value={eventData.end}
                onChange={(e) => setEventData(prev => ({ ...prev, end: e.target.value }))}
              />
            </div>
          </div>
          <div>
            <Label htmlFor="message">Message</Label>
            <Textarea
              id="message"
              value={eventData.message}
              onChange={(e) => setEventData(prev => ({ ...prev, message: e.target.value }))}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Generation Tabs */}
      <Tabs defaultValue="digital" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="digital">Digital Invite</TabsTrigger>
          <TabsTrigger value="printable">Printable Invite</TabsTrigger>
          <TabsTrigger value="description">Event Description</TabsTrigger>
        </TabsList>

        {/* Digital Invite Tab */}
        <TabsContent value="digital" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Digital Invite Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label>Theme</Label>
                  <Select value={digitalOptions.theme} onValueChange={(value) => 
                    setDigitalOptions(prev => ({ ...prev, theme: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="modern">Modern</SelectItem>
                      <SelectItem value="classic">Classic</SelectItem>
                      <SelectItem value="elegant">Elegant</SelectItem>
                      <SelectItem value="fun">Fun</SelectItem>
                      <SelectItem value="minimal">Minimal</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Color Scheme</Label>
                  <Select value={digitalOptions.colorScheme} onValueChange={(value) => 
                    setDigitalOptions(prev => ({ ...prev, colorScheme: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="warm">Warm</SelectItem>
                      <SelectItem value="cool">Cool</SelectItem>
                      <SelectItem value="neutral">Neutral</SelectItem>
                      <SelectItem value="vibrant">Vibrant</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Button 
                    onClick={generateDigitalInvite} 
                    disabled={loading.digital}
                    className="w-full"
                  >
                    {loading.digital ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-4 w-4" />
                        Generate Digital Invite
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              {/* Digital Invite Result */}
              {results.digital && (
                <div className="mt-4 p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Result:</h4>
                  {results.digital.success ? (
                    <div className="space-y-2">
                      <p className="text-green-600">✅ Generation successful!</p>
                      {results.digital.data?.svgContent && (
                        <div className="border p-2 rounded bg-gray-50">
                          <p className="text-sm font-medium mb-2">Generated SVG:</p>
                          <div 
                            className="max-h-40 overflow-auto text-xs bg-white p-2 rounded border"
                            dangerouslySetInnerHTML={{ __html: results.digital.data.svgContent }}
                          />
                        </div>
                      )}
                      {results.digital.usage && (
                        <div className="text-sm text-gray-600">
                          <p>Generation time: {results.digital.usage.generationTime}ms</p>
                          <p>Remaining quota: {results.digital.usage.remainingQuota}</p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-red-600">❌ {results.digital.error}</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Printable Invite Tab */}
        <TabsContent value="printable" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Printable Invite Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label>Paper Size</Label>
                  <Select value={printableOptions.paperSize} onValueChange={(value) => 
                    setPrintableOptions(prev => ({ ...prev, paperSize: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="A4">A4</SelectItem>
                      <SelectItem value="A5">A5</SelectItem>
                      <SelectItem value="A6">A6</SelectItem>
                      <SelectItem value="photo4x6">4x6 Photo</SelectItem>
                      <SelectItem value="photo5x7">5x7 Photo</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Orientation</Label>
                  <Select value={printableOptions.orientation} onValueChange={(value) => 
                    setPrintableOptions(prev => ({ ...prev, orientation: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="portrait">Portrait</SelectItem>
                      <SelectItem value="landscape">Landscape</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Theme</Label>
                  <Select value={printableOptions.theme} onValueChange={(value) => 
                    setPrintableOptions(prev => ({ ...prev, theme: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="elegant">Elegant</SelectItem>
                      <SelectItem value="classic">Classic</SelectItem>
                      <SelectItem value="modern">Modern</SelectItem>
                      <SelectItem value="minimal">Minimal</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Button 
                    onClick={generatePrintableInvite} 
                    disabled={loading.printable}
                    className="w-full"
                  >
                    {loading.printable ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-4 w-4" />
                        Generate Printable
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              {/* Printable Invite Result */}
              {results.printable && (
                <div className="mt-4 p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Result:</h4>
                  {results.printable.success ? (
                    <div className="space-y-2">
                      <p className="text-green-600">✅ Generation successful!</p>
                      {results.printable.data?.svgContent && (
                        <div className="border p-2 rounded bg-gray-50">
                          <p className="text-sm font-medium mb-2">Generated SVG:</p>
                          <div 
                            className="max-h-40 overflow-auto text-xs bg-white p-2 rounded border"
                            dangerouslySetInnerHTML={{ __html: results.printable.data.svgContent }}
                          />
                        </div>
                      )}
                      {results.printable.usage && (
                        <div className="text-sm text-gray-600">
                          <p>Generation time: {results.printable.usage.generationTime}ms</p>
                          <p>Remaining quota: {results.printable.usage.remainingQuota}</p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-red-600">❌ {results.printable.error}</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Event Description Tab */}
        <TabsContent value="description" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Event Description Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label>Tone</Label>
                  <Select value={descriptionOptions.tone} onValueChange={(value) => 
                    setDescriptionOptions(prev => ({ ...prev, tone: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="formal">Formal</SelectItem>
                      <SelectItem value="casual">Casual</SelectItem>
                      <SelectItem value="friendly">Friendly</SelectItem>
                      <SelectItem value="professional">Professional</SelectItem>
                      <SelectItem value="exciting">Exciting</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Length</Label>
                  <Select value={descriptionOptions.length} onValueChange={(value) => 
                    setDescriptionOptions(prev => ({ ...prev, length: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="short">Short</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="long">Long</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="includeDetails"
                    checked={descriptionOptions.includeDetails}
                    onChange={(e) => setDescriptionOptions(prev => ({ ...prev, includeDetails: e.target.checked }))}
                  />
                  <Label htmlFor="includeDetails">Include Details</Label>
                </div>
                <div>
                  <Button 
                    onClick={generateEventDescription} 
                    disabled={loading.description}
                    className="w-full"
                  >
                    {loading.description ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-4 w-4" />
                        Generate Description
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              {/* Event Description Result */}
              {results.description && (
                <div className="mt-4 p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Result:</h4>
                  {results.description.success ? (
                    <div className="space-y-2">
                      <p className="text-green-600">✅ Generation successful!</p>
                      {results.description.data && (
                        <div className="space-y-3">
                          <div>
                            <p className="text-sm font-medium text-gray-600">Title:</p>
                            <p className="font-semibold">{results.description.data.title}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-600">Description:</p>
                            <p className="whitespace-pre-wrap">{results.description.data.description}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-600">Short Description:</p>
                            <p className="text-sm">{results.description.data.shortDescription}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-600">Keywords:</p>
                            <div className="flex flex-wrap gap-1">
                              {results.description.data.keywords?.map((keyword: string, index: number) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {keyword}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                      {results.description.usage && (
                        <div className="text-sm text-gray-600 mt-3">
                          <p>Generation time: {results.description.usage.generationTime}ms</p>
                          <p>Remaining quota: {results.description.usage.remainingQuota}</p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-red-600">❌ {results.description.error}</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
