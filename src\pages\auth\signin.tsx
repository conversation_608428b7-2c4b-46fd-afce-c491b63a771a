'use client';
import { SignInForm } from '@/components/SignIn';
import { useSession } from 'next-auth/react';
import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { Footer } from '@/components/Footer';

export default function SignInPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'authenticated') {
      // Check session storage first, then query params, then fall back to /events
      const redirectUrl = sessionStorage.getItem("redirectUrl") ||
        (router.query.redirectUrl as string) ||
        '/events';

      // Clear the session storage after using it to prevent future redirects
      sessionStorage.removeItem("redirectUrl");

      // Redirect to the appropriate page
      router.push(redirectUrl);
    }
  }, [session, status, router]);

  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1 flex items-center justify-center">
        <SignInForm />
      </main>
      <Footer type="app" />
    </div>
  );
}