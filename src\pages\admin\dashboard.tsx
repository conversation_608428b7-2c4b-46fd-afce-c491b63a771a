import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/layouts/AdminLayout";
import { withAdminAuth } from "@/lib/auth/admin";
import { GetServerSideProps } from "next";
import Link from "next/link";

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    users: 0,
    events: 0,
    invites: 0,
    feedback: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch dashboard stats
    async function fetchStats() {
      try {
        // Fetch user count
        const usersResponse = await fetch('/api/admin/users/count');
        if (usersResponse.ok) {
          const usersData = await usersResponse.json();
          setStats(prev => ({
            ...prev,
            users: usersData.count
          }));
        }

        // For feedback count, use our new feedback API
        const feedbackResponse = await fetch('/api/admin/feedback?limit=0');
        if (feedbackResponse.ok) {
          const feedbackData = await feedbackResponse.json();
          setStats(prev => ({
            ...prev,
            feedback: feedbackData.pagination.total
          }));
        }

        // Add other stats fetching here when implemented

      } catch (error) {
        console.error("Failed to load dashboard stats:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchStats();
  }, []);

  return (
    <AdminLayout pageTitle="Dashboard">
      <h1 className="text-2xl font-bold mb-6">Admin Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-blue-50 p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold text-blue-700">Users</h3>
          <p className="text-3xl font-bold mt-2">{loading ? '...' : stats.users}</p>
          <p className="text-sm text-gray-500 mt-1">Total registered users</p>
          <div className="mt-3">
            <Link href="/admin/users" className="text-sm text-blue-600 hover:text-blue-800 font-medium">
              Manage Users →
            </Link>
          </div>
        </div>

        <div className="bg-green-50 p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold text-green-700">Events</h3>
          <p className="text-3xl font-bold mt-2">{loading ? '...' : stats.events}</p>
          <p className="text-sm text-gray-500 mt-1">Total events created</p>
        </div>

        <div className="bg-purple-50 p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold text-purple-700">Invites</h3>
          <p className="text-3xl font-bold mt-2">{loading ? '...' : stats.invites}</p>
          <p className="text-sm text-gray-500 mt-1">Total invitations sent</p>
        </div>

        <div className="bg-rose-50 p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold text-rose-700">Feedback</h3>
          <p className="text-3xl font-bold mt-2">{loading ? '...' : stats.feedback}</p>
          <p className="text-sm text-gray-500 mt-1">Total feedback received</p>
          <div className="mt-3">
            <Link href="/admin/feedback" className="text-sm text-rose-600 hover:text-rose-800 font-medium">
              Open Feedback Inbox →
            </Link>
          </div>
        </div>
      </div>

      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
        <div className="bg-white border rounded-lg p-4">
          <p className="text-gray-500 text-center py-4">No recent activity to display.</p>
        </div>
      </div>
    </AdminLayout>
  );
}

// Server-side protection for admin routes
export const getServerSideProps: GetServerSideProps = withAdminAuth();