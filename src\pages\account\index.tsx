"use client"

import { useState, useEffect, use<PERSON>allback } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/router"
import { useSession } from "next-auth/react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { ChevronLeft, Fingerprint, CreditCard, Download, Plus, Check, AlertCircle, Mail, Clock, Loader2, Receipt, FileText } from "lucide-react"
import dayjs from "@/lib/dayjs"
import { withPageAuth } from "@/lib/auth/page"
import { useGoogleAuth } from "@/hooks/useGoogleAuth"
import { useEventLimits } from "@/hooks/useEventLimits"
import { useToast } from "@/components/ui/use-toast"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface User {
  name: string
  email: string
  image?: string
  hasGoogleLinked: boolean
  // hasPasskeys: boolean
  id?: string
  googleEmail?: string
}

interface PaymentTransaction {
  id: string
  date: Date
  amount: number
  description: string
  status: string
  paymentMethod: string
  eventId?: string
  plan?: string
  receiptUrl?: string | null
  invoiceUrl?: string | null
}

function AccountManagement() {
  const router = useRouter();
  const { data: session } = useSession();
  const { linkGoogleAccount, unlinkGoogleAccount, isLoading } = useGoogleAuth();
  const eventLimits = useEventLimits();
  const { toast } = useToast();
  const [user, setUser] = useState<User>({
    name: "Sarah Johnson",
    email: "<EMAIL>",
    hasGoogleLinked: false,
    // hasPasskeys: false,
  });

  const [transactions, setTransactions] = useState<PaymentTransaction[]>([]);
  const [isLoadingPayments, setIsLoadingPayments] = useState(false);

  // Load user data from session when available
  useEffect(() => {
    if (session?.user) {
      // Fetch the user profile to check if Google is linked
      fetch('/api/user/profile')
        .then(res => res.json())
        .then(data => {
          if (data.success) {            setUser({
              name: session.user.name || "User",
              email: session.user.email,
              image: session.user.image || data.image,
              hasGoogleLinked: data.hasGoogleLinked || false,
              // hasPasskeys: false,
              id: session.user.id,
              googleEmail: data.googleEmail || "",
            });
          }
        })
        .catch(error => {
          console.error("Error fetching user profile:", error);
        });
    }
    
    // Check for link success/error in URL parameters
    const linkSuccess = router.query.linkSuccess === 'true';
    const linkError = router.query.linkError === 'true';
    
    if (linkSuccess) {
      toast({
        title: "Google account linked",
        description: "Your Google account has been successfully linked to your account",
      });
      // Remove the query parameter to prevent showing the toast again on refresh
      router.replace('/account', undefined, { shallow: true });
    } else if (linkError) {
      toast({
        title: "Error linking Google account",
        description: "There was a problem linking your Google account. Please try again.",
        variant: "destructive",
      });
      // Remove the query parameter to prevent showing the toast again on refresh
      router.replace('/account', undefined, { shallow: true });
    }
  }, [session, router, toast]);

  // Fetch payment history when the payments tab is selected or on initial load
  const fetchPaymentHistory = useCallback(async () => {
    if (!session) return;
    
    setIsLoadingPayments(true);
    try {
      const response = await fetch('/api/payments/history');
      if (!response.ok) {
        throw new Error('Failed to fetch payment history');
      }
      
      const data = await response.json();
      // Convert date strings back to Date objects
      setTransactions(data.payments.map((payment: any) => ({
        ...payment,
        date: new Date(payment.date)
      })));
    } catch (error) {
      console.error('Error fetching payment history:', error);
      toast({
        title: "Error fetching payment history",
        description: "There was a problem loading your payment history. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingPayments(false);
    }
  }, [session, toast]);

  useEffect(() => {
    if (session) {
      fetchPaymentHistory();
    }
  }, [session, fetchPaymentHistory]);

  const handleTabChange = (value: string) => {
    if (value === 'payments' && transactions.length === 0) {
      fetchPaymentHistory();
    }
  };
  const handleGoogleLinkToggle = () => {
    if (user.hasGoogleLinked) {
      unlinkGoogleAccount();
    } else {
      linkGoogleAccount();
    }
  }

  // const handleCreatePasskey = () => {
  //   // In a real app, you would initiate the WebAuthn registration process
  //   console.log("Creating passkey...")
  //   setTimeout(() => {
  //     setUser((prev) => ({
  //       ...prev,
  //       hasPasskeys: true,
  //     }))
  //   }, 1500)
  // }

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "succeeded":
      case "completed":
        return "bg-green-100 text-green-800"
      case "pending":
      case "processing":
        return "bg-yellow-100 text-yellow-800"
      case "failed":
      case "canceled":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }
  
  const getStatusLabel = (status: string) => {
    switch (status) {
      case "succeeded":
        return "completed";
      case "requires_payment_method":
        return "failed";
      default:
        return status;
    }
  }

  const getPlanName = (planId: string) => {
    switch (planId) {
      case 'host_plus':
        return 'Host+';
      case 'host_pro':
        return 'Host Pro';
      default:
        return planId.charAt(0).toUpperCase() + planId.slice(1);
    }
  }

  const navigateToCreateEvent = () => {
    // If the user has reached their event limits, show a toast with information
    if (!eventLimits.canCreateEvent) {
      let message = "";
      
      if (eventLimits.hasReachedUpcomingLimit && eventLimits.hasReachedTotalLimit) {
        message = `You've reached both your limit of ${eventLimits.limits.upcomingEvents} upcoming events and ${eventLimits.limits.totalEvents} total events.`;
      } else if (eventLimits.hasReachedUpcomingLimit) {
        message = `You've reached your limit of ${eventLimits.limits.upcomingEvents} upcoming events.`;
      } else {
        message = `You've reached your limit of ${eventLimits.limits.totalEvents} total events.`;
      }
      
      toast({
        title: "Event limit reached",
        description: message + " Please delete an existing event before creating a new one.",
        // variant: "warning",
      });
      return;
    }
    
    router.push("/event/new");
  };

  return (
    <div className="flex flex-col  bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b sticky top-0 z-10">
        <div className="container mx-auto px-4">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <Image 
                  src="/iac-logo-standard.svg" 
                  alt="I am Coming" 
                  className="h-8 w-auto"
                  width={180}
                  height={32}
                />
              </Link>
              <div className="ml-4 flex items-center text-sm text-gray-500">
                <Link href="/events" className="flex items-center hover:text-primary">
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Back to Events
                </Link>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="flex items-center space-x-4">
                <Button variant="outline" size="sm" onClick={navigateToCreateEvent}>
                  <Plus className="mr-1 h-4 w-4" />
                  New Event
                </Button>
                
                <div className="relative">
                  <Avatar className="h-8 w-8 cursor-pointer">
                    <AvatarImage src={user.image} alt={user.name} />
                    <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                  </Avatar>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Page Title Bar */}
      <div className="bg-gray-50 border-b">
        <div className="container mx-auto px-4 py-4 flex items-center">
          <h1 className="text-2xl font-bold">Account Management</h1>
        </div>
      </div>

      {/* Main Content */}
      <main className="flex-1 container mx-auto p-4 md:p-6">
        {/* Profile Section */}
        <Card className="mb-6">
          <CardHeader className="flex flex-row items-center gap-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={user.image} alt={user.name} />
              <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
            </Avatar>
            <div>
              <CardTitle>{user.name}</CardTitle>
              <CardDescription>{user.email}</CardDescription>
            </div>
          </CardHeader>
        </Card>

        {/* Tabs */}
        <Tabs defaultValue="authentication" className="w-full" onValueChange={handleTabChange}>
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="authentication">Authentication</TabsTrigger>
            <TabsTrigger value="payments">Payment History</TabsTrigger>
          </TabsList>

          <TabsContent value="authentication">
            <Card>
              <CardHeader>
                <CardTitle>Authentication Methods</CardTitle>
                <CardDescription>Manage how you sign in to your account</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Email */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-primary/10 p-2 rounded-full">
                      <Mail className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium">Email</h3>
                      <p className="text-sm text-gray-500">{user.email}</p>
                    </div>
                  </div>
                  <Badge>Primary</Badge>
                </div>

                <Separator />

                {/* Google Account */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-primary/10 p-2 rounded-full">
                      <svg className="h-5 w-5" viewBox="0 0 24 24">
                        <path
                          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                          fill="#4285F4"
                        />
                        <path
                          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                          fill="#34A853"
                        />
                        <path
                          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                          fill="#FBBC05"
                        />
                        <path
                          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                          fill="#EA4335"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium">Google Account</h3>
                      <p className="text-sm text-gray-500">
                        {user.hasGoogleLinked
                          ? `Your Google account (${user.googleEmail || 'linked'}) is connected`
                          : "Link your Google account for easier sign-in"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {isLoading ? (
                      <Button size="sm" variant="outline" disabled>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        {user.hasGoogleLinked ? "Unlinking..." : "Linking..."}
                      </Button>
                    ) : (
                      <Switch 
                        id="google-link" 
                        checked={user.hasGoogleLinked} 
                        onCheckedChange={handleGoogleLinkToggle}
                        disabled={isLoading}
                      />
                    )}
                    <Label htmlFor="google-link" className="sr-only">
                      Link Google Account
                    </Label>
                  </div>
                </div>                <Separator />

                {/* Passkeys */}
                {/* <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-primary/10 p-2 rounded-full">
                      <Fingerprint className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium">Passkeys</h3>
                      <p className="text-sm text-gray-500">
                        {user.hasPasskeys
                          ? "You have passkeys set up for this account"
                          : "Create a passkey for passwordless sign-in"}
                      </p>
                    </div>
                  </div>
                  {user.hasPasskeys ? (
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                      <Check className="mr-1 h-3 w-3" />
                      Active
                    </Badge>
                  ) : (
                    <Button variant="outline" size="sm" onClick={handleCreatePasskey}>
                      <Plus className="mr-1 h-3 w-3" />
                      Create Passkey
                    </Button>
                  )}
                </div> */}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="payments">
            <Card>
              <CardHeader>
                <CardTitle>Payment History</CardTitle>
                <CardDescription>View your past transactions and download receipts</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingPayments ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : transactions.length > 0 ? (
                  <div>
                    <div className="flex justify-end mb-4">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={fetchPaymentHistory}
                        disabled={isLoadingPayments}
                      >
                        {isLoadingPayments ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <svg 
                            xmlns="http://www.w3.org/2000/svg" 
                            className="h-4 w-4 mr-2" 
                            viewBox="0 0 24 24" 
                            fill="none" 
                            stroke="currentColor" 
                            strokeWidth="2" 
                            strokeLinecap="round" 
                            strokeLinejoin="round"
                          >
                            <path d="M21 2v6h-6" />
                            <path d="M3 12a9 9 0 0 1 15-6.7l3-3" />
                            <path d="M3 22v-6h6" />
                            <path d="M21 12a9 9 0 0 1-15 6.7l-3 3" />
                          </svg>
                        )}
                        Refresh
                      </Button>
                    </div>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Payment Method</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {transactions.map((transaction) => (
                          <TableRow key={transaction.id}>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-gray-500" />
                                {dayjs(transaction.date).format("MMM D, YYYY")}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div>
                                {transaction.description || `${getPlanName(transaction.plan || '')} Plan`}
                                {transaction.eventId && (
                                  <div className="text-xs text-gray-500 mt-1">
                                    Event ID: {transaction.eventId}
                                  </div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>${transaction.amount.toFixed(2)}</TableCell>
                            <TableCell>
                              <Badge className={getStatusColor(transaction.status)}>
                                {getStatusLabel(transaction.status)}
                              </Badge>
                            </TableCell>
                            <TableCell>{transaction.paymentMethod}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end space-x-1">
                                {transaction.receiptUrl && (
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Button variant="ghost" size="icon" onClick={() => window.open(transaction.receiptUrl as string, '_blank')}>
                                          <Receipt className="h-4 w-4" />
                                        </Button>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>View Receipt</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                )}
                                
                                {transaction.invoiceUrl && (
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Button variant="ghost" size="icon" onClick={() => window.open(transaction.invoiceUrl as string, '_blank')}>
                                          <FileText className="h-4 w-4" />
                                        </Button>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>View Invoice</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CreditCard className="h-10 w-10 mx-auto text-gray-400 mb-2" />
                    <h3 className="text-lg font-medium text-gray-900">No payment history</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      You haven&apos;t made any payments yet.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}

export const getServerSideProps = withPageAuth()

export default AccountManagement