import { use<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormField, FormMessage } from "./ui/form";
import { Label } from "./ui/label";
import { Input } from "./ui/input";
import { Button } from "./ui/button";
import { Alert, AlertDescription } from "./ui/alert";
import Link from "next/link";
import { Loader2 } from "lucide-react";
import React, { useState } from "react";
import { signIn } from "next-auth/react";
import { useRouter } from "next/router";

const credentialsSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
  name: z.string().min(1, "Name is required").optional(),
});
const credentialsSignInSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
});

type CredentialsFormValues = z.infer<typeof credentialsSchema>;
type CredentialsSignInFormValues = z.infer<typeof credentialsSignInSchema>;

type PasswordSignInProps = {
  error: string;
  isLoading: boolean;
  showSignUp: boolean;
  setShowSignUp: (show: boolean) => void;
};

export function PasswordSignIn({ error, isLoading, showSignUp, setShowSignUp }: PasswordSignInProps) {
  const [localError, setLocalError] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const credentialsForm = useForm<CredentialsFormValues>({
    resolver: zodResolver(credentialsSchema),
    defaultValues: { email: "", password: "", name: "" },
  });
  const credentialsSignInForm = useForm<CredentialsSignInFormValues>({
    resolver: zodResolver(credentialsSignInSchema),
    defaultValues: { email: "", password: "" },
  });

  const handleCredentialsSignIn: SubmitHandler<CredentialsSignInFormValues> = async (formData) => {
    setLoading(true);
    setLocalError("");

    console.log("Form data:", formData);


    try {
      const redirectUrl = sessionStorage.getItem("redirectUrl") || (router.query.callbackUrl as string) || "/events";
      const result = await signIn("credentials", {
        email: formData.email,
        password: formData.password,
        redirect: false,
        callbackUrl: redirectUrl,
      });
      if (result?.error) {
        setLocalError("Invalid email or password");
      }
    } catch (error) {
      setLocalError(error instanceof Error ? error.message : "Failed to sign in");
    } finally {
      setLoading(false);
    }
  };

  const handleSignUp: SubmitHandler<CredentialsFormValues> = async (formData) => {
    setLoading(true);
    setLocalError("");
    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          name: formData.name,
        }),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || "Failed to sign up");
      }
      // Auto sign in after successful signup
      await handleCredentialsSignIn(formData);
    } catch (error) {
      setLocalError(error instanceof Error ? error.message : "Failed to sign up");
    } finally {
      setLoading(false);
    }
  };

  return !showSignUp ? (
    <FormProvider {...credentialsForm}>
      <form onSubmit={credentialsSignInForm.handleSubmit(handleCredentialsSignIn)} className="space-y-4">
        <FormField
          control={credentialsSignInForm.control}
          name="email"
          render={({ field }) => (
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" placeholder="<EMAIL>" className="h-11 shadow-none border-[#E2E8F0]" {...field} />
              <FormMessage />
            </div>
          )}
        />
        <FormField
          control={credentialsSignInForm.control}
          name="password"
          render={({ field }) => (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">Password</Label>
                <Link href="/auth/forgot-password" className="text-xs text-primary hover:underline">
                  Forgot password?
                </Link>
              </div>
              <Input id="password" type="password" placeholder="••••••••" className="h-11 shadow-none border-[#E2E8F0]" {...field} />
              <FormMessage />
            </div>
          )}
        />
        {(error || localError) && (
          <Alert variant="destructive">
            <AlertDescription>{error || localError}</AlertDescription>
          </Alert>
        )}
        <Button variant="primary-button" type="submit" className="w-full h-11 mt-2" disabled={isLoading || loading}>
          {isLoading || loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Signing in...
            </>
          ) : (
            "Sign in"
          )}
        </Button>
      </form>
      <div className="mt-6 text-center">
        <p className="text-sm text-muted-foreground">
          Don&apos;t have an account?{" "}
          <Button variant="link" className="p-0 h-auto" onClick={() => setShowSignUp(true)}>
            Sign up
          </Button>
        </p>
      </div>
    </FormProvider>
  ) : (
    <FormProvider {...credentialsForm}>
      <form onSubmit={credentialsForm.handleSubmit(handleSignUp)} className="space-y-4">
        <FormField
          control={credentialsForm.control}
          name="name"
          render={({ field }) => (
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input id="name" type="text" placeholder="Your name" className="h-11 shadow-none border-[#E2E8F0]" {...field} />
              <FormMessage />
            </div>
          )}
        />
        <FormField
          control={credentialsForm.control}
          name="email"
          render={({ field }) => (
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" placeholder="<EMAIL>" className="h-11 shadow-none border-[#E2E8F0]" {...field} />
              <FormMessage />
            </div>
          )}
        />
        <FormField
          control={credentialsForm.control}
          name="password"
          render={({ field }) => (
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input id="password" type="password" placeholder="••••••••" className="h-11 shadow-none border-[#E2E8F0]" {...field} />
              <FormMessage />
            </div>
          )}
        />
        {(error || localError) && (
          <Alert variant="destructive">
            <AlertDescription>{error || localError}</AlertDescription>
          </Alert>
        )}
        <Button type="submit" variant="primary-button" className="w-full h-11 mt-2" disabled={isLoading || loading}>
          {isLoading || loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating account...
            </>
          ) : (
            "Create account"
          )}
        </Button>
      </form>
      <div className="mt-6 text-center">
        <p className="text-sm text-muted-foreground">
          Already have an account?{" "}
          <Button variant="link" className="p-0 h-auto" onClick={() => setShowSignUp(false)}>
            Sign in
          </Button>
        </p>
      </div>
    </FormProvider>
  );
}
