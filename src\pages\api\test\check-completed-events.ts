import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';

/**
 * Manual test endpoint for checking completed events
 * This allows developers to manually trigger the completed events check
 * without waiting for the actual cron schedule
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Require authentication for test endpoints
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Authentication required for test endpoints' });
    }

    // Optional: Restrict to admin users only
    // if (!session.user.isAdmin) {
    //   return res.status(403).json({ error: 'Admin access required' });
    // }

    const db = Database.getInstance();

    // In test mode, only process specific test events to avoid emailing real users
    const testEventIds = ['E2i8pe45d']; // Add more test event IDs here as needed

    console.log('🧪 TEST MODE: Only processing test events:', testEventIds);

    const eventsToCheck = [];
    for (const eventId of testEventIds) {
      try {
        const event = await db.readData('events', eventId);
        if (event) {
          // Check if this test event should be processed (same logic as production)
          const eventDate = new Date(event.eventDate);
          const twentyFourHoursAgo = new Date();
          twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

          if (eventDate < twentyFourHoursAgo && event.status !== 'completed') {
            eventsToCheck.push({ id: eventId, ...event });
          } else {
            eventsToCheck.push({
              id: eventId,
              ...event,
              _testNote: 'Event not past due or already completed'
            });
          }
        } else {
          eventsToCheck.push({
            id: eventId,
            _testNote: 'Event not found in database'
          });
        }
      } catch (error) {
        console.error(`Error checking test event ${eventId}:`, error);
        eventsToCheck.push({
          id: eventId,
          _testNote: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      }
    }

    let processedCount = 0;
    let errorCount = 0;
    let emailsSent = 0;
    const results = [];

    for (const event of eventsToCheck) {
      try {
        // Get organization to check email eligibility before processing
        const organization = await db.getOrganizationById(event.ownerAccountId);
        const willSendEmail = organization && !organization.contactGroupSettings?.hasBeenAsked;

        // Check if event has contact groups
        const { hasContactGroupsToSave } = await import('@/lib/saved-contact-groups');
        const hasGroups = await hasContactGroupsToSave(event.id);

        // Mark event as completed and trigger post-event actions (same as cron job)
        await db.markEventAsCompleted(event.id, event.ownerAccountId);

        processedCount++;

        // Track if email was likely sent
        if (willSendEmail && hasGroups) {
          emailsSent++;
        }

        results.push({
          eventId: event.id,
          eventName: event.eventName || 'Unknown Event',
          status: willSendEmail && hasGroups ? 'email_sent' : 'processed',
          message: willSendEmail && hasGroups
            ? 'Event completed and introduction email sent'
            : willSendEmail && !hasGroups
            ? 'Event completed but no contact groups to save'
            : 'Event completed but organization already asked'
        });

        console.log(`Processed completed event: ${event.eventName} (${event.id}) - Email sent: ${willSendEmail && hasGroups}`);

      } catch (error) {
        errorCount++;
        results.push({
          eventId: event.id,
          eventName: event.eventName || 'Unknown Event',
          status: 'error',
          message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
        });

        console.error(`Error processing event ${event.id}:`, error);
      }
    }

    return res.status(200).json({
      success: true,
      message: `Processed ${processedCount} events, sent ${emailsSent} emails, ${errorCount} errors`,
      totalEvents: eventsToCheck.length,
      processedCount,
      errorCount,
      emailsSent,
      results
    });

  } catch (error) {
    console.error('Error in manual completed events check:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
