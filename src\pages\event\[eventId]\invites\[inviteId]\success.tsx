"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/router"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Check, Copy, Download, Share2, Mail, MessageSquare, Loader2 } from "lucide-react"
import { useInvite } from "@/hooks/useInvites"
import { useEvent } from "@/hooks/useEvent"
import { useToast } from "@/components/ui/use-toast"
import { useSendInviteEmail } from "@/hooks/useSendInviteEmail"
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout"
import { generateDownloadInviteLink } from "@/lib/utils"

export default function InviteSuccessPage() {
  const router = useRouter()
  const { eventId, inviteId } = router.query
  const { invite, loading } = useInvite(eventId as string, inviteId as string)
  const { event } = useEvent(eventId as string)
  const [copied, setCopied] = useState(false)
  const { sendEmail, sendingEmail } = useSendInviteEmail()
  const { toast } = useToast()

  // Generate the RSVP link
  const rsvpLink = typeof window !== 'undefined'
    ? `${window.location.origin}/event/${eventId}/rsvp/${inviteId}`
    : `https://iamcoming.io/event/${eventId}/rsvp/${inviteId}`

  // Handle copy link
  const copyLink = () => {
    navigator.clipboard.writeText(rsvpLink)
    setCopied(true)
    toast({
      title: "Link copied!",
      description: "The invite link has been copied to your clipboard.",
    })
    setTimeout(() => setCopied(false), 2000)
  }

  // Handle download invite - opens in a new tab
  const downloadInvite = () => {
    if (!invite || !eventId || !inviteId) return;

    // Generate API URL with print settings
    const apiUrl = generateDownloadInviteLink({
      eventId: eventId as string,
      ID: inviteId as string
    }, event?.printSettings);

    // Open the URL in a new tab
    window.open(apiUrl, '_blank');
    
    toast({
      title: "Downloading invite",
      description: "Your invite is downloading in a new tab.",
    });
  };

  // Handle share invite
  const shareInvite = async () => {
    // Include the URL in the text itself to ensure it's shared properly
    const shareText = `RSVP for ${event?.eventName}\n${rsvpLink}`;

    if (navigator.share) {
      try {
        // Don't include the URL parameter, as some platforms prioritize it over text
        // Instead, include the URL in the text itself
        await navigator.share({
          title: `RSVP for ${event?.eventName}`,
          text: shareText,
        })
        toast({
          title: "Shared successfully!",
          description: "Your invite has been shared.",
        })
      } catch (error) {
        console.error("Error sharing:", error)
      }
    } else {
      // Fallback to copy the formatted text instead of just the link
      navigator.clipboard.writeText(shareText);
      setCopied(true);
      toast({
        title: "Text copied!",
        description: "The invite text has been copied to your clipboard.",
      })
      setTimeout(() => setCopied(false), 2000)
    }
  }

  // Handle send email
  const handleSendEmail = () => {
    if (invite?.email) {
      sendEmail(eventId as string, inviteId as string, invite.email);
    }
  }

  // Text message functionality will be implemented in the future
  // Removed sendText function as it's marked as "Coming Soon"

  // Handle done button
  const handleDone = () => {
    router.push(`/event/${eventId}/invites/${inviteId}`)
  }

  if (loading) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </ProtectedLayout>
    )
  }

  return (
    <ProtectedLayout>
      <div className="flex items-center justify-center min-h-[calc(100vh-64px)] bg-gray-50 p-4">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="pt-6 pb-4 px-6">
            <div className="flex flex-col items-center text-center">
              {/* Success icon */}
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <Check className="h-8 w-8 text-green-500" />
              </div>

              {/* Success message */}
              <h2 className="text-2xl font-semibold mb-2">Invite Created Successfully</h2>
              <p className="text-gray-500 mb-6">
                Invite for {invite?.name} has been created<br />
                Copy link and share it with your guests
              </p>

              {/* RSVP Link */}
              <div className="w-full mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1 text-left">RSVP Link</label>
                <div className="flex">
                  <Input
                    value={rsvpLink}
                    readOnly
                    className="flex-1 bg-gray-50"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    className="ml-2"
                    onClick={copyLink}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Action buttons */}
              <div className="grid grid-cols-2 gap-3 w-full mb-4">
                <Button variant="outline" onClick={downloadInvite} className="flex items-center justify-center">
                  <Download className="h-4 w-4 mr-2" />
                  Download Invite
                </Button>
                <Button variant="outline" onClick={shareInvite} className="flex items-center justify-center">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share Invite
                </Button>
              </div>

              {/* Availability message */}
              {!invite?.email && !invite?.phone && (
                <div className="w-full py-1 px-4 bg-yellow-50 border border-amber-300 text-center rounded-md text-sm mb-3">
                  Email & phone number is not available
                </div>
              )}

              {/* Send invitation buttons */}
              <Button
                variant="outline"
                onClick={handleSendEmail}
                className="flex items-center justify-center w-full mb-3"
                disabled={!invite?.email || sendingEmail}
              >
                {sendingEmail ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Mail className="h-4 w-4 mr-2" />
                    <span className="hidden sm:inline">Send invitation over email</span>
                    <span className="sm:hidden">Email Invite</span>
                  </>
                )}
              </Button>

              {/* Phone availability message */}
              {invite?.email && !invite?.phone && (
                <div className="w-full py-1 px-4 bg-yellow-50 border border-yellow-100 text-center rounded-md text-sm mb-3">
                  Phone number is not available
                </div>
              )}

              <Button
                variant="outline"
                className="flex items-center justify-center w-full mb-6"
                disabled={true}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Send invitation as a text</span>
                <span className="sm:hidden">Text Invite</span>
                <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">Coming Soon</span>
              </Button>

              {/* Done button */}
              <Button
                variant='primary-button'
                onClick={handleDone}
                className="w-full"
              >
                Done
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </ProtectedLayout>
  )
}
