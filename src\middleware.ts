import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const url = request.nextUrl.pathname;

  // Check if the request is for the specific RSVP page
  if (url.startsWith('/event/') && url.includes('/rsvp/')) {
    const response = NextResponse.next();

    // Allow the X-Frame-Options header
    response.headers.set('X-Frame-Options', 'ALLOWALL');


    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/event/:eventId/rsvp/:inviteId'],
};
