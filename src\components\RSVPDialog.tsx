import React, { useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle
} from "@/components/ui/dialog"
import { Minus, Plus, Globe } from "lucide-react"
import { Event, EventInvite } from "@/types"
import { z } from "zod"
import { useForm, FormProvider } from "react-hook-form"
import { RadioGroup, RadioGroupItem } from "./ui/radio-group"
import { useRSVP } from "@/hooks/useRSVP"
import { Textarea } from "./ui/textarea"
import { Badge } from "./ui/badge"
import { ScrollArea } from "./ui/scroll-area"

interface RSVPDialogProps {
  rsvpDialogOpen: boolean;
  setRsvpDialogOpen: (open: boolean) => void;
  event: Event;
  invite: EventInvite;
  onRSVPResponded: (status: string, response: {
    adults: number;
    children: number;
    message: string;
    status: string;
    email: string;
    phone?: string;
  }) => void;
}

const RSVPFormSchema = z.object({
  ID: z.string().optional(),
  adults: z.number().optional(),
  children: z.number().optional(),
  message: z.string().max(1000, "Message cannot exceed 1000 characters").optional(),
  status: z.enum(['invited', 'accepted', 'declined']),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().refine((val) => val === "" || /^\+?[1-9]\d{1,14}$|^\d{3}-\d{3}-\d{4}$/.test(val), {
    message: "Please enter a valid phone number",
  }).optional(),
})

export default function RSVPDialog({
  rsvpDialogOpen,
  setRsvpDialogOpen,
  event,
  invite,
  onRSVPResponded
}: RSVPDialogProps) {

  const { loading, error, rsvpInvite } = useRSVP(event.ID, invite.ID)
  const eventTimezone = event.timezone || "Australia/Melbourne";

  // Check if RSVP deadline has passed
  const isRsvpClosed = React.useMemo(() => {
    if (!event.rsvpDueDate) return false;

    const now = new Date();
    const dueDate = new Date(event.rsvpDueDate);

    return now > dueDate;
  }, [event.rsvpDueDate]);

  // Track when RSVP dialog is opened
  useEffect(() => {
    if (rsvpDialogOpen && event.ID && invite.ID) {
      // Track RSVP initiated event
      fetch(`/api/event/${event.ID}/invites/${invite.ID}/track-rsvp-initiated`, {
        method: 'POST',
      }).catch(err => console.error('Error tracking RSVP initiation:', err));
    }
  }, [rsvpDialogOpen, event.ID, invite.ID]);

  const form = useForm<z.infer<typeof RSVPFormSchema>>({
    values: {
      ID: invite?.ID || '',
      adults: invite?.response?.adults || invite?.adults || 0,
      children: invite?.response?.children || invite?.children || 0,
      message: invite?.response?.message || '',
      status: invite?.status || "invited",
      email: invite?.email || '',
      phone: invite?.phone || '',
    }
  })

  function handleCountChange(type: "adult" | "children", action: "increment" | "decrement") {
    const currentValue = form.getValues(type === 'adult' ? 'adults' : 'children') || 0;
    form.setValue(type === 'adult' ? 'adults' : 'children', action === "increment" ? currentValue + 1 : currentValue - 1);
  };

  async function handleRSVPSubmit(formData: z.infer<typeof RSVPFormSchema>) {
    // Check if RSVP deadline has passed
    if (isRsvpClosed) {
      console.error("RSVP deadline has passed");
      return;
    }

    if (formData.status === "declined") {
      formData.adults = 0;
      formData.children = 0;
    }

    if (formData.status === "accepted" || formData.status === "declined") {
      try {
        const response = await rsvpInvite({
          status: formData.status,
          adults: formData.adults || 0,
          children: formData.children || 0,
          message: formData.message || '',
          email: formData.email,
          phone: formData.phone || '',
        });

        // Check if the response contains an error about RSVP deadline
        if (response?.error === 'RSVP deadline has passed') {
          console.error("RSVP deadline has passed");
          return;
        }

        onRSVPResponded(formData.status, {
          adults: formData.adults || 0,
          children: formData.children || 0,
          message: formData.message || '',
          status: formData.status,
          email: formData.email,
          phone: formData.phone || '',
        });
      } catch (error) {
        console.error("Error submitting RSVP:", error);
      }
    } else {
      console.error("Invalid status:", formData.status);
    }
  };

  return (
    <Dialog open={rsvpDialogOpen} onOpenChange={setRsvpDialogOpen}>
      <DialogContent className="sm:max-w-md max-h-[90vh] flex flex-col p-0 gap-0">
        <FormProvider {...form}>
          <form onSubmit={form.handleSubmit(handleRSVPSubmit)} className="flex flex-col h-full">
            <DialogHeader className="flex-shrink-0 p-6 pb-2">
              <DialogTitle>RSVP to {event.eventName}</DialogTitle>
              <DialogDescription>
                {isRsvpClosed
                  ? "The RSVP deadline for this event has passed."
                  : "Please confirm your attendance details below."}
              </DialogDescription>
              <div className="flex items-center text-xs text-gray-500 mt-1">
                <Globe className="h-3.5 w-3.5 mr-1 inline-block" />
                <span>All times are in timezone: </span>
                <Badge variant="outline" className="ml-1 text-xs px-1 py-0">{eventTimezone}</Badge>
              </div>
              {event.rsvpDueDate && (
                <div className="text-xs text-gray-500 mt-1">
                  <span>RSVP deadline: </span>
                  <Badge variant={isRsvpClosed ? "destructive" : "outline"} className="ml-1 text-xs px-1 py-0">
                    {new Date(event.rsvpDueDate).toLocaleDateString()} at {new Date(event.rsvpDueDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                  </Badge>
                </div>
              )}
            </DialogHeader>
              <ScrollArea className="h-96">
                <div className="flex-1 overflow-y-auto px-6 scroll-smooth">
                  {isRsvpClosed ? (
                    <div className="py-8 text-center">
                      <div className="bg-red-50 border border-red-100 rounded-md p-4 mb-4">
                        <h3 className="font-medium text-red-800 mb-1">RSVP Deadline Passed</h3>
                        <p className="text-sm text-gray-600">
                          The deadline for responding to this invitation has passed.
                          If you need to change your response, please contact the host directly.
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-6 py-2">
                      <div className="space-y-2">
                        <Label>Your Response</Label>
                        <RadioGroup
                          defaultValue={form.getValues("status")}
                          onValueChange={(value) => form.setValue("status", value as "accepted" | "declined")}
                          className="flex space-x-4"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="accepted" id="accept" />
                            <Label htmlFor="accept" className="cursor-pointer">
                              Accept
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="declined" id="decline" />
                            <Label htmlFor="decline" className="cursor-pointer">
                              Decline
                            </Label>
                          </div>
                        </RadioGroup>
                      </div>

                      {form.watch("status") === "accepted" && (
                        <>
                          <div className="space-y-2">
                            <Label>Number of Adults</Label>
                            <div className="flex items-center space-x-2">
                              <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={() => handleCountChange("adult", "decrement")}
                              >
                                <Minus className="h-4 w-4" />
                              </Button>
                              <Input
                                type="number"
                                {...form.register("adults")}
                                className="w-20 text-center"
                                min="0"
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={() => handleCountChange("adult", "increment")}
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Number of Children</Label>
                            <div className="flex items-center space-x-2">
                              <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={() => handleCountChange("children", "decrement")}
                              >
                                <Minus className="h-4 w-4" />
                              </Button>
                              <Input
                                type="number"
                                {...form.register("children")}
                                className="w-20 text-center"
                                min="0"
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={() => handleCountChange("children", "increment")}
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </>
                      )}

                      <div className="space-y-2">
                        <Label htmlFor="message">Message to Host</Label>
                        <p className="text-sm text-gray-500">
                          Share any dietary requirements, special needs, or nice wishes with the host.
                        </p>
                        <div className="relative">
                          <Textarea
                            id="message"
                            {...form.register("message")}
                            placeholder="Enter your message"
                            className="min-h-[100px] resize-none"
                            maxLength={1000}
                          />
                          <div className="absolute bottom-2 right-2 text-xs text-gray-500">
                            {form.watch("message")?.length || 0}/1000
                          </div>
                        </div>
                        {form.formState.errors.message && (
                          <p className="text-sm text-red-500">{form.formState.errors.message.message}</p>
                        )}
                      </div>

                      <div className="space-y-4 pt-4 border-t">
                        <div>
                          <h3 className="font-medium mb-1">Contact Information</h3>
                          <p className="text-sm text-gray-500">
                            Your email and phone will be used to send updates about this event. This information can be used by the host to contact you.
                          </p>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="email">Email *</Label>
                          <Input
                            id="email"
                            type="email"
                            {...form.register("email")}
                            placeholder="Enter your email"
                            required
                            autoComplete="email"
                          />
                          {form.formState.errors.email && (
                            <p className="text-sm text-red-500">{form.formState.errors.email.message}</p>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="phone">Phone (Optional)</Label>
                          <p className="text-sm text-gray-500">
                            Having your phone number is helpful for the host to contact you if needed.
                          </p>
                          <Input
                            id="phone"
                            type="tel"
                            {...form.register("phone")}
                            placeholder="Enter your phone number"
                            autoComplete="tel"
                          />
                          {form.formState.errors.phone && (
                            <p className="text-sm text-red-500">{form.formState.errors.phone.message}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </ScrollArea>
            <DialogFooter className="flex-shrink-0 p-6 pt-4 mt-2 border-t">
              <Button variant="outline" type="button" onClick={() => setRsvpDialogOpen(false)} disabled={loading}>
                {isRsvpClosed ? "Close" : "Cancel"}
              </Button>
              {!isRsvpClosed && (
                <Button variant={"primary-button"} type="submit" disabled={loading}>
                  {loading ? "Submitting..." : "Confirm RSVP"}
                </Button>
              )}
            </DialogFooter>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  )
}
