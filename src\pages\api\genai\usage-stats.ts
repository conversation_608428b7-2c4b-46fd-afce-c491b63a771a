/**
 * API endpoint for getting GenAI usage statistics
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { GenAIRateLimiter } from '@/lib/genai/rate-limiter';
import { Database } from '@/lib/database';
import { log } from '@/lib/logger';
import { rateLimit } from '@/lib/rateLimiter';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Apply basic rate limiting
  if (!rateLimit(req, res, 30, 60 * 1000)) {
    return res.status(429).json({
      error: 'Too many requests, please try again later',
      retryAfter: res.getHeader('Retry-After')
    });
  }

  try {
    // Check authentication
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { eventId } = req.query;
    const userId = session.user.id;

    // Initialize services
    const rateLimiter = new GenAIRateLimiter();
    const db = Database.getInstance();

    // Get user usage statistics
    const userStats = await rateLimiter.getUserUsageStats(userId);

    // Get event-specific statistics if eventId is provided
    let eventStats = null;
    if (eventId && typeof eventId === 'string') {
      const event = await db.readData('events', eventId);
      
      if (!event) {
        return res.status(404).json({ error: 'Event not found' });
      }

      // Check if user has permission to view this event's stats
      const isOwner = event.ownerAccountId === userId;
      const isManager = event.managers && event.managers.includes(userId);

      if (!isOwner && !isManager) {
        return res.status(403).json({ error: 'Permission denied' });
      }

      eventStats = {
        eventId,
        eventName: event.eventName,
        aiUsageCounters: event.aiUsageCounters || {
          digitalInviteGenerations: 0,
          printableInviteGenerations: 0,
          descriptionGenerations: 0,
          totalGenerations: 0,
          lastGenerationDate: null,
        },
      };
    }

    // Get user's AI access status
    const user = await db.readData('users', userId);
    const hasAiAccess = user?.hasAiAccess === true;

    // Determine user type (this could be enhanced to check actual subscription status)
    const userType = user?.plan === 'paid' ? 'paid' : 'free';

    // Get recent generations (last 10)
    const recentGenerations = await getRecentGenerations(userId, eventId as string);

    const response = {
      user: {
        id: userId,
        hasAiAccess,
        userType,
        dailyUsage: userStats.dailyUsage,
        remainingQuota: userStats.remainingQuota,
        resetTime: userStats.resetTime,
        totalGenerations: userStats.totalGenerations,
      },
      event: eventStats,
      recentGenerations,
      limits: {
        free: {
          dailyLimit: 5,
          perEventLimit: 3,
        },
        paid: {
          dailyLimit: 50,
          perEventLimit: 10,
        },
      },
    };

    log('Usage stats retrieved', { userId, eventId, hasAiAccess });

    return res.status(200).json(response);

  } catch (error) {
    console.error('Usage stats error:', error);
    log('Usage stats error', { error });
    
    return res.status(500).json({ 
      error: 'Internal server error' 
    });
  }
}

/**
 * Get recent AI generations for a user/event
 */
async function getRecentGenerations(userId: string, eventId?: string): Promise<any[]> {
  try {
    const db = Database.getInstance();
    
    // This would need to be implemented based on how we store generation history
    // For now, return empty array as placeholder
    return [];
    
  } catch (error) {
    console.error('Failed to get recent generations:', error);
    return [];
  }
}

// Configure API route
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
};
